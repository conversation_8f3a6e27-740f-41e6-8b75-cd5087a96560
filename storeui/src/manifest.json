{"name": "金刚到家系统", "appid": "__UNI__A63D235", "description": "金刚到家系统", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"optimization": {"subPackages": true}, "safearea": {"bottom": {"offset": "none"}}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {}, "sdkConfigs": {"ad": {}}, "icons": {"android": {"hdpi": "", "xhdpi": "", "xxhdpi": "", "xxxhdpi": ""}, "ios": {"appstore": "", "ipad": {"app": "", "app@2x": "", "notification": "", "notification@2x": "", "proapp@2x": "", "settings": "", "settings@2x": "", "spotlight": "", "spotlight@2x": ""}, "iphone": {"app@2x": "", "app@3x": "", "notification@2x": "", "notification@3x": "", "settings@2x": "", "settings@3x": "", "spotlight@2x": "", "spotlight@3x": ""}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx3aa06d66a59ba971", "setting": {"urlCheck": true, "es6": true, "minified": true, "postcss": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false}, "optimization": {"subPackages": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation"], "lazyCodeLoading": "requiredComponents"}, "mp-alipay": {"usingComponents": true, "component2": true}, "mp-qq": {"optimization": {"subPackages": true}, "appid": ""}, "mp-baidu": {"usingComponents": true, "appid": ""}, "mp-toutiao": {"usingComponents": true, "appid": ""}, "h5": {"template": "", "router": {"mode": "history", "base": "/h5/"}, "optimization": {"treeShaking": {"enable": false}}, "title": "金刚到家系统", "domain": "https://store.jingangai.cn", "sdkConfigs": {"maps": {"qqmap": {"key": "VG5BZ-IQQCU-XCNVG-4IWN3-M3UFV-AEBQG"}, "tencent": {"key": "VG5BZ-IQQCU-XCNVG-4IWN3-M3UFV-AEBQG"}}}, "devServer": {"https": false}}}