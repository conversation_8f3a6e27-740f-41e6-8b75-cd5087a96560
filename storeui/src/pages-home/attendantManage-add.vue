<template>
  <view class="page">
    <appHead fixed title="快速员工入驻">
      <template #left>
        <view @click="handleBackNavigation" class="custom-back-btn">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
          <text class="f24" style="color: #333; margin-left: 8rpx;">{{ isInvitationMode ? '登录' : '返回' }}</text>
        </view>
      </template>
    </appHead>

    <!-- 邀请信息提示 -->
    <view v-if="isInvitationMode && storeInfo" class="invitation-banner">
      <view class="invitation-content">
        <u-icon name="account-fill" color="#1890ff" size="20"></u-icon>
        <text class="invitation-text">
          <text class="inviter-name">{{ storeInfo.inviter_name }}</text>
          ({{ storeInfo.inviter_role }}) 邀请您加入
          <text class="store-name">{{ storeInfo.store_name }}</text>
        </text>
      </view>
    </view>

    <!-- 装饰性背景元素 -->
    <view class="bg-decoration">
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>
    </view>

    <!-- 表单内容区域 -->
    <scroll-view scroll-y class="form-container">
      <view class="form-section">
        <view class="form-group">
          <!-- 1. 身份证人像页照片（非必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label">身份证人像页照片</text>
            </view>
            <view class="upload-section">
              <view class="upload-area" @click="uploadIdCard">
                <image v-if="form.idCardImage" :src="form.idCardImage" mode="aspectFill" class="uploaded-image"></image>
                <view v-else class="upload-placeholder">
                  <u-icon name="camera-fill" size="40" color="#CCCCCC"></u-icon>
                  <text class="upload-text">点击上传身份证照片</text>
                </view>
              </view>
            </view>
            <text class="field-description">上传身份证人像页，快速识别个人信息，可选填</text>
          </view>

          <!-- 2. 姓名（必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">姓名</text>
            </view>
            <input type="text" v-model="form.name" placeholder="请输入服务人员姓名" class="input" />
            <text class="field-description">请填写真实姓名，与身份证上的姓名保持一致</text>
          </view>

          <!-- 3. 身份证号（必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">身份证号</text>
            </view>
            <input type="text" v-model="form.idCard" placeholder="请输入正确的身份证号码" class="input" maxlength="18" @blur="onIdCardChange" />
            <text class="field-description">请输入18位身份证号码，用于实名认证</text>
          </view>

          <!-- 4. 生日（必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">生日</text>
            </view>
            <picker mode="date" :value="form.birthday" @change="onBirthdayChange" class="date-picker">
              <view class="selector-text" :class="{ placeholder: !form.birthday }">
                {{ form.birthday || '请选择生日' }}
              </view>
            </picker>
            <text class="field-description">选择出生日期，可从身份证自动识别</text>
          </view>

          <!-- 4. 产品（必填，多选） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">服务产品</text>
            </view>
            <view class="skill-selector" @click="showProductPicker">
              <text class="selector-text" :class="{ placeholder: !selectedProductsText }">
                {{ selectedProductsText || '请选择服务产品（可多选）' }}
              </text>
              <u-icon name="arrow-right" color="#CCCCCC" size="24"></u-icon>
            </view>
            <text class="field-description">选择您能够提供的服务产品，可以选择多项，如家政清洁、母婴护理等</text>
          </view>

          <!-- 5. 手机号（必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">手机号</text>
              <button v-if="isInvitationMode"
                      ref="wechatPhoneBtn"
                      class="wechat-phone-btn"
                      open-type="getPhoneNumber"
                      @getphonenumber="getPhoneNumber">
                微信授权
              </button>
            </view>
            <input type="number" v-model="form.phone" placeholder="请输入手机号码" class="input" maxlength="11" />
            <text class="field-description">
              {{ isInvitationMode ? '建议使用"微信授权"快速获取手机号' : '请输入常用手机号，用于接收订单通知和客户联系' }}
            </text>
          </view>

          <!-- 6. 接单时间（必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">接单时间</text>
            </view>
            <view class="time-selector" @click="showTimePicker">
              <text class="selector-text" :class="{ placeholder: !workTimeText }">
                {{ workTimeText || '请选择接单时间段' }}
              </text>
              <u-icon name="arrow-right" color="#CCCCCC" size="24"></u-icon>
            </view>
            <text class="field-description">设置您每天可以接单的时间段，如8时-18时</text>
          </view>

          <!-- 7. 指定休息日（可选） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label">指定休息日</text>
            </view>
            <view class="rest-day-selector" @click="showRestDayPicker">
              <text class="selector-text" :class="{ placeholder: !selectedRestDaysText }">
                {{ selectedRestDaysText || '请选择休息日（可多选）' }}
              </text>
              <u-icon name="arrow-right" color="#CCCCCC" size="24"></u-icon>
            </view>
            <text class="field-description">选择您每周的休息日，可选择多天，如周六、周日（可选填）</text>
          </view>

          <!-- 8. 常住地址（必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label required">常住地址</text>
            </view>
            <view class="address-selector" @click="selectAddress">
              <u-icon name="map" color="#CCCCCC" size="24"></u-icon>
              <text class="selector-text" :class="{ placeholder: !form.address }">
                {{ form.address || '点击选择常住地址' }}
              </text>
              <u-icon name="arrow-right" color="#CCCCCC" size="24"></u-icon>
            </view>
            <text class="field-description">点击选择地址或手动输入详细居住地址，便于就近安排工作</text>
          </view>

          <!-- 9. 门牌号（非必填） -->
          <view class="form-item">
            <view class="label-wrapper">
              <text class="label">门牌号</text>
            </view>
            <input type="text" v-model="form.doorNumber" placeholder="请输入门牌号（选填）" class="input" />
            <text class="field-description">如有具体门牌号可填写，方便精确定位，可不填</text>
          </view>

          <!-- 10. 邀请码（仅在非邀请模式下显示） -->
          <view v-if="!isInvitationMode && currentUserInfo" class="form-item">
            <view class="label-wrapper">
              <text class="label">邀请码</text>
            </view>
            <view class="invitation-code-input">
              <text class="invitation-code-value">{{ currentUserInfo.id }}</text>
              <button class="copy-code-btn" @click="copyInvitationCode">
                复制
              </button>
            </view>
            <text class="field-description">您的专属邀请码，可通过右上角"..."菜单转发给朋友，邀请他们快速入驻</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button class="submit-btn" @click="handleSubmit" :disabled="isSubmitting">
        {{ isSubmitting ? '提交中...' : '完成入驻' }}
      </button>
    </view>

    <!-- 产品选择弹窗 - 二级分类滚动选择器 -->
    <u-popup :show="productPickerShow" mode="bottom" @close="productPickerShow = false">
      <view class="product-picker-container">
        <view class="picker-header">
          <text class="cancel-btn" @click="productPickerShow = false">取消</text>
          <text class="title">选择服务产品</text>
          <text class="confirm-btn" @click="confirmProductSelection">确定</text>
        </view>
        <view class="picker-content">
          <scroll-view scroll-y class="product-scroll-container">
            <view v-for="(category, categoryIndex) in productCategories" :key="categoryIndex" class="product-category">
              <view class="category-header">
                <view class="category-left" @click="toggleCategory(categoryIndex)">
                  <text class="category-name">{{ category.name }}</text>
                  <text class="category-count">({{ category.products.length }}项)</text>
                </view>
                <view class="category-actions">
                  <view
                    class="batch-select-btn"
                    :class="{ 'selected': isCategoryAllSelected(categoryIndex) }"
                    @click.stop="toggleCategoryBatch(categoryIndex)"
                  >
                    <u-icon
                      :name="isCategoryAllSelected(categoryIndex) ? 'checkmark-circle-fill' : 'checkmark-circle'"
                      :color="isCategoryAllSelected(categoryIndex) ? '#1890ff' : '#999'"
                      size="18"
                    ></u-icon>
                    <text class="batch-text" :class="{ 'active': isCategoryAllSelected(categoryIndex) }">
                      {{ isCategoryAllSelected(categoryIndex) ? '已全选' : '全选' }}
                    </text>
                  </view>
                  <view class="expand-btn" @click="toggleCategory(categoryIndex)">
                    <u-icon
                      :name="category.expanded ? 'arrow-up' : 'arrow-down'"
                      color="#666"
                      size="20"
                    ></u-icon>
                  </view>
                </view>
              </view>
              <view v-if="category.expanded !== false" class="products-grid">
                <view
                  v-for="(product, productIndex) in category.products"
                  :key="productIndex"
                  class="product-item"
                  :class="{ active: isProductSelected(product) }"
                  @click="toggleProduct(product)"
                >
                  <text class="product-text">{{ product.name || product }}</text>
                  <u-icon
                    v-if="isProductSelected(product)"
                    name="checkmark"
                    color="#1890ff"
                    size="16"
                  ></u-icon>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view class="selected-products-preview">
          <text class="preview-label">已选择：</text>
          <text class="preview-text">{{ selectedProductsPreview }}</text>
        </view>
      </view>
    </u-popup>

    <!-- 休息日选择弹窗 -->
    <xyj-choice
      :show.sync="restDayPickerShow"
      :choiceList="restDayOptions"
      :isMultiple="true"
      confirmText="确定"
      @choiceConfirm="onRestDayConfirm"
    />

    <!-- 时间段选择弹窗 -->
    <u-popup :show="timePickerShow" mode="bottom" @close="timePickerShow = false">
      <view class="time-picker-container">
        <view class="picker-header">
          <text class="cancel-btn" @click="timePickerShow = false">取消</text>
          <text class="title">选择接单时间</text>
          <text class="confirm-btn" @click="confirmWorkTime">确定</text>
        </view>
        <view class="picker-content">
          <view class="time-range-picker">
            <view class="time-column">
              <text class="column-title">开始时间</text>
              <picker-view :value="[startTimeIndex]" @change="onStartTimeChange" class="time-picker-view">
                <picker-view-column>
                  <view class="time-item" v-for="(time, index) in timeOptions" :key="index">
                    {{ time }}
                  </view>
                </picker-view-column>
              </picker-view>
            </view>
            <view class="time-column">
              <text class="column-title">结束时间</text>
              <picker-view :value="[endTimeIndex]" @change="onEndTimeChange" class="time-picker-view">
                <picker-view-column>
                  <view class="time-item" v-for="(time, index) in timeOptions" :key="index">
                    {{ time }}
                  </view>
                </picker-view-column>
              </picker-view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 确认提交对话框 -->
    <u-popup :show="confirmDialogShow" mode="center" @close="cancelConfirm">
      <view class="confirm-dialog">
        <view class="dialog-header">
          <text class="dialog-title">确认入驻信息</text>
          <text class="dialog-subtitle">请仔细核对以下信息，确认无误后提交</text>
        </view>

        <scroll-view scroll-y class="dialog-content">
          <view class="info-section">
            <view class="info-item">
              <text class="info-label">姓名：</text>
              <text class="info-value">{{ form.name || '未填写' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">身份证号：</text>
              <text class="info-value">{{ form.idCard || '未填写' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">生日：</text>
              <text class="info-value">{{ form.birthday || '未填写' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">手机号：</text>
              <text class="info-value">{{ form.phone || '未填写' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">服务产品：</text>
              <text class="info-value">{{ selectedProductsText || '未选择' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">接单时间：</text>
              <text class="info-value">{{ workTimeText || '未设置' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">休息日：</text>
              <text class="info-value">{{ selectedRestDaysText || '未选择' }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">常住地址：</text>
              <text class="info-value">{{ form.address || '未填写' }}</text>
            </view>

            <view class="info-item" v-if="form.doorNumber">
              <text class="info-label">门牌号：</text>
              <text class="info-value">{{ form.doorNumber }}</text>
            </view>
          </view>
        </scroll-view>

        <view class="dialog-footer">
          <button class="cancel-btn" @click="cancelConfirm" :disabled="isSubmitting">取消</button>
          <button class="confirm-btn" @click="confirmSubmit" :disabled="isSubmitting">
            {{ isSubmitting ? '提交中...' : '确定提交' }}
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import { post, get } from '../utlis/require.js'
  import { registerStaff } from '../api/staff.js'

  export default {
    data() {
      return {
        // 表单数据
        form: {
          name: '',
          idCard: '',
          birthday: '',
          phone: '',
          address: '',
          doorNumber: '',
          idCardImage: '',        // 本地临时图片路径
          idCardImageUrl: '',     // 上传后的图片URL
          latitude: '',
          longitude: '',
          products: [], // 产品名称列表
          productIds: [], // 产品ID列表
          workStartTime: '',
          workEndTime: '',
          restDays: []
        },

        // 邀请码相关
        invitationCode: '',      // 邀请码
        storeInfo: null,         // 门店信息
        isInvitationMode: false, // 是否为邀请模式
        currentUserInfo: null,   // 当前登录用户信息

        // 订单分享相关
        fromOrderShare: false,   // 是否来自订单分享
        orderNumber: '',         // 订单号
        shareUserMobile: '',     // 分享用户手机号
        shareUserOpenid: '',     // 分享用户openid

        // 身份证识别相关
        idCardRecognition: {
          frontBase64: '',
          isRecognizing: false,
          recognizedData: {
            name: '',
            idNumber: ''
          }
        },

        // 产品选项 - 二级分类结构
        productCategories: [],
        productsLoading: false,
        // 原始产品数据（从API获取）
        originalProducts: [],
        // 默认产品选项（API失败时使用）
        defaultProductCategories: [
          {
            name: '家政清洁',
            products: [
              { id: 'default_1', name: '日常保洁' },
              { id: 'default_2', name: '深度清洁' },
              { id: 'default_3', name: '开荒保洁' },
              { id: 'default_4', name: '家电清洗' },
              { id: 'default_5', name: '地毯清洗' }
            ]
          },
          {
            name: '母婴护理',
            products: [
              { id: 'default_6', name: '月嫂服务' },
              { id: 'default_7', name: '育儿嫂' },
              { id: 'default_8', name: '催乳师' },
              { id: 'default_9', name: '产后修复' },
              { id: 'default_10', name: '婴儿护理' }
            ]
          },
          {
            name: '老人护理',
            products: [
              { id: 'default_11', name: '居家护理' },
              { id: 'default_12', name: '陪护服务' },
              { id: 'default_13', name: '康复护理' },
              { id: 'default_14', name: '医疗陪护' },
              { id: 'default_15', name: '生活照料' }
            ]
          },
          {
            name: '专业服务',
            products: [
              { id: 'default_16', name: '营养配餐' },
              { id: 'default_17', name: '健康管理' },
              { id: 'default_18', name: '心理咨询' },
              { id: 'default_19', name: '按摩理疗' },
              { id: 'default_20', name: '家庭教育' }
            ]
          }
        ],

        // 休息日选项
        restDayOptions: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],

        // 时间选项（0-23时）
        timeOptions: [],

        // 弹窗控制
        productPickerShow: false,
        restDayPickerShow: false,
        timePickerShow: false,
        confirmDialogShow: false,

        // 提交状态控制
        isSubmitting: false,

        // 时间选择器索引
        startTimeIndex: 8, // 默认8时
        endTimeIndex: 18,  // 默认18时

        // 验证规则
        rules: {
          name: [
            { required: true, message: '请输入姓名', trigger: ['blur', 'change'] }
          ],
          idCard: [
            { required: true, message: '请输入身份证号', trigger: ['blur', 'change'] },
            {
              validator: (rule, value, callback) => {
                if (!value) return true;
                return uni.$u.test.idCard(value);
              },
              message: '身份证号格式不正确',
              trigger: ['blur', 'change']
            }
          ],
          phone: [
            { required: true, message: '请输入手机号', trigger: ['blur', 'change'] },
            {
              validator: (rule, value, callback) => {
                if (!value) return true;
                return uni.$u.test.mobile(value);
              },
              message: '手机号格式不正确',
              trigger: ['blur', 'change']
            }
          ],
          address: [
            { required: true, message: '请输入常住地址', trigger: ['blur', 'change'] }
          ]
        }
      };
    },

    computed: {
      // 已选产品文本
      selectedProductsText() {
        return this.form.products.length > 0 ? this.form.products.join('、') : '';
      },

      // 已选休息日文本
      selectedRestDaysText() {
        return this.form.restDays.length > 0 ? this.form.restDays.join('、') : '';
      },

      // 工作时间文本
      workTimeText() {
        if (this.form.workStartTime && this.form.workEndTime) {
          return `${this.form.workStartTime}-${this.form.workEndTime}`;
        }
        return '';
      },

      // 已选产品预览文本（用于弹窗底部显示）
      selectedProductsPreview() {
        if (this.form.products.length === 0) {
          return '请选择产品';
        }
        if (this.form.products.length <= 3) {
          return this.form.products.join('、');
        }
        return `${this.form.products.slice(0, 3).join('、')} 等${this.form.products.length}项`;
      }
    },

    created() {
      this.initTimeOptions();
      // 移除产品获取调用，改为在邀请码验证成功后或用户信息获取后调用
      // this.getProductList(); // ❌ 此时邀请码还未设置
      this.getCurrentUserInfo();
    },

    onLoad(options) {
      console.log('员工入驻页面加载，参数:', options);

      // 检查是否有邀请码参数
      if (options.invitation_code) {
        this.invitationCode = options.invitation_code;
        this.isInvitationMode = true;
        this.loadStoreInfoByInvitation();
      }

      // 检查是否来自订单分享
      if (options.from === 'order-share') {
        this.fromOrderShare = true;
        this.orderNumber = options.orderNumber;
        this.shareUserMobile = options.mobile;
        this.shareUserOpenid = options.openid;
        console.log('来自订单分享，订单号:', this.orderNumber);
      }
    },

    onShow() {
      // 页面显示时重新获取用户信息
      console.log('页面显示，重新获取用户信息');
      console.log('当前邀请码状态:', this.invitationCode);
      console.log('当前邀请模式状态:', this.isInvitationMode);
      this.getCurrentUserInfo();
    },

    // 微信小程序分享
    onShareAppMessage() {
      // 实时获取用户信息，确保最新状态
      const userInfo = this.$store.state.user || this.currentUserInfo;

      if (userInfo && userInfo.id) {
        const invitationCode = userInfo.id;
        const invitationPath = `/pages-home/attendantManage-add?invitation_code=${invitationCode}`;

        console.log('分享时的用户信息:', userInfo);
        console.log('生成的邀请码:', invitationCode);

        return {
          title: `${userInfo.name || '同事'}邀请您加入${userInfo.store_name || '我们团队'}`,
          path: invitationPath,
          imageUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png'
        };
      }

      // 默认分享内容
      return {
        title: '快速员工入驻',
        path: '/pages-home/attendantManage-add',
        imageUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png'
      };
    },

    methods: {
      // 处理返回导航
      handleBackNavigation() {
        if (this.isInvitationMode) {
          // 邀请模式下返回到员工端登录页面
          console.log('邀请模式：返回到员工端登录页面');
          uni.reLaunch({
            url: '/pages/login/login'
          });
        } else {
          // 普通模式下返回上一页
          console.log('普通模式：返回上一页');
          uni.navigateBack({
            fail: () => {
              // 如果无法返回，则跳转到首页
              uni.reLaunch({
                url: '/pages/home/<USER>'
              });
            }
          });
        }
      },

      // 获取当前用户信息
      getCurrentUserInfo() {
        try {
          // 从store中获取用户信息（正确的路径是state.user）
          const userInfo = this.$store.state.user;
          console.log('Store中的用户信息:', userInfo);
          console.log('Store状态:', this.$store.state);

          if (userInfo && userInfo.id) {
            this.currentUserInfo = userInfo;
            console.log('当前用户信息设置成功:', this.currentUserInfo);

            // 如果不是邀请模式且没有邀请码，使用当前用户ID作为邀请码
            if (!this.isInvitationMode && !this.invitationCode) {
              this.invitationCode = String(userInfo.id);
              console.log('自动设置邀请码为当前用户ID:', this.invitationCode);
              // 设置邀请码后，获取产品列表
              this.getProductList();
            }
          } else {
            console.log('未获取到用户信息，尝试从本地存储获取');
            // 尝试从本地存储获取
            try {
              const cachedUser = uni.getStorageSync('userInfo');
              if (cachedUser && cachedUser.id) {
                this.currentUserInfo = cachedUser;
                console.log('从本地存储获取用户信息成功:', this.currentUserInfo);

                // 如果不是邀请模式且没有邀请码，使用当前用户ID作为邀请码
                if (!this.isInvitationMode && !this.invitationCode) {
                  this.invitationCode = String(cachedUser.id);
                  console.log('自动设置邀请码为缓存用户ID:', this.invitationCode);
                  // 设置邀请码后，获取产品列表
                  this.getProductList();
                }
              } else {
                console.log('本地存储中也没有用户信息');
              }
            } catch (storageError) {
              console.error('从本地存储获取用户信息失败:', storageError);
            }
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
        }
      },

      // 复制邀请码
      copyInvitationCode() {
        if (!this.currentUserInfo || !this.currentUserInfo.id) {
          uni.showToast({
            title: '获取邀请码失败',
            icon: 'error'
          });
          return;
        }

        uni.setClipboardData({
          data: String(this.currentUserInfo.id),
          success: () => {
            uni.showToast({
              title: '邀请码已复制',
              icon: 'success'
            });
          },
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'error'
            });
          }
        });
      },



      // 根据邀请码加载门店信息
      async loadStoreInfoByInvitation() {
        try {
          uni.showLoading({ title: '加载邀请信息...' });

          console.log('开始验证邀请码:', this.invitationCode);
          const response = await get(`/api/v1/public/product/invitation-info/${this.invitationCode}`);
          console.log('邀请码验证响应:', response);

          // 由于前端request.js会提取数据，直接检查response是否有数据
          if (response && response.inviter_name) {
            this.storeInfo = response;
            console.log('邀请码验证成功，门店信息:', this.storeInfo);
            uni.showToast({
              title: `${this.storeInfo.inviter_name}邀请您加入${this.storeInfo.store_name}`,
              icon: 'success',
              duration: 3000
            });

            // 邀请码验证成功后，自动获取产品列表
            this.getProductList();

            // 邀请码验证成功后，自动尝试获取微信手机号
            this.autoGetWechatPhone();
          } else {
            console.log('邀请码验证失败，响应数据:', response);
            uni.showToast({
              title: response?.message || response?.msg || '邀请码无效',
              icon: 'error'
            });
            // 邀请码无效，跳转回首页
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        } catch (error) {
          console.error('加载邀请信息失败:', error);
          uni.showToast({
            title: error?.message || error?.msg || '加载邀请信息失败',
            icon: 'error'
          });
        } finally {
          uni.hideLoading();
        }
      },

      // 自动提示获取微信手机号（被邀请人进入页面时）
      async autoGetWechatPhone() {
        try {
          console.log('邀请模式：提示自动获取微信手机号');

          // 只在邀请模式下执行
          if (!this.isInvitationMode) {
            console.log('非邀请模式，跳过自动获取手机号');
            return;
          }

          // 延迟一下，确保邀请信息加载完成和Toast显示完毕
          setTimeout(() => {
            uni.showModal({
              title: '快速填写',
              content: `欢迎加入${this.storeInfo.store_name}！\n\n是否使用微信手机号快速填写信息？`,
              showCancel: true,
              cancelText: '手动输入',
              confirmText: '微信授权',
              success: (res) => {
                if (res.confirm) {
                  // 用户同意，自动点击微信授权按钮
                  this.triggerWechatPhoneAuth();
                } else {
                  console.log('用户选择手动输入手机号');
                }
              }
            });
          }, 2000);
        } catch (error) {
          console.error('自动获取微信手机号异常:', error);
          // 异常情况下保留手动输入功能
        }
      },

      // 触发微信手机号授权
      triggerWechatPhoneAuth() {
        try {
          console.log('准备触发微信授权');

          // 在小程序中，我们需要提示用户点击微信授权按钮
          // 因为微信授权必须由用户主动触发，不能通过代码自动触发
          uni.showToast({
            title: '请点击上方"微信授权"按钮',
            icon: 'none',
            duration: 3000
          });

          // 可以通过滚动到手机号字段来引导用户注意
          this.scrollToPhoneField();

        } catch (error) {
          console.error('触发微信授权失败:', error);
          uni.showToast({
            title: '请手动点击微信授权',
            icon: 'none'
          });
        }
      },

      // 滚动到手机号字段
      scrollToPhoneField() {
        try {
          // 使用页面滚动到手机号字段位置
          uni.pageScrollTo({
            selector: '.form-item:nth-child(5)', // 手机号是第5个表单项
            duration: 500
          });
        } catch (error) {
          console.error('滚动到手机号字段失败:', error);
        }
      },

      // 微信手机号快速验证
      async getPhoneNumber(e) {
        if (!this.isInvitationMode) {
          uni.showToast({
            title: '请通过邀请链接进入',
            icon: 'error'
          });
          return;
        }

        try {
          const { code, encryptedData, iv } = e.detail;
          if (!code) {
            uni.showToast({
              title: '获取手机号失败',
              icon: 'error'
            });
            return;
          }

          uni.showLoading({ title: '获取手机号...' });

          // 先获取微信登录code
          const loginRes = await this.wxLogin();
          if (!loginRes.code) {
            throw new Error('微信登录失败');
          }

          // 调用后端解密手机号（使用正确的API路径）
          const response = await post('/api/v1/common/decryptWechatPhone', {
            wx_code: loginRes.code,
            encrypted_data: encryptedData,
            iv: iv
          }, {
            contentType: 'application/json'
          });

          if (response && response.phone_number) {
            this.form.phone = response.phone_number;
            uni.showToast({
              title: '手机号获取成功',
              icon: 'success'
            });
          } else {
            uni.showToast({
              title: '手机号解密失败',
              icon: 'error'
            });
          }
        } catch (error) {
          console.error('获取手机号失败:', error);
          uni.showToast({
            title: '获取手机号失败',
            icon: 'error'
          });
        } finally {
          uni.hideLoading();
        }
      },

      // 微信登录
      wxLogin() {
        return new Promise((resolve, reject) => {
          uni.login({
            provider: 'weixin',
            success: resolve,
            fail: reject
          });
        });
      },

      // 初始化时间选项
      initTimeOptions() {
        this.timeOptions = [];
        for (let i = 0; i < 24; i++) {
          this.timeOptions.push(`${i}时`);
        }
        // 设置默认时间
        this.form.workStartTime = this.timeOptions[this.startTimeIndex];
        this.form.workEndTime = this.timeOptions[this.endTimeIndex];
      },

      // 获取产品列表
      async getProductList() {
        if (this.productsLoading) return;

        // 检查是否有邀请码，没有邀请码则不能获取产品
        if (!this.invitationCode) {
          console.warn('没有邀请码，无法获取产品列表');
          this.originalProducts = [];
          this.productCategories = [];
          return;
        }

        this.productsLoading = true;

        try {
          // 使用统一的请求方法调用产品列表API（公开接口）
          // 传递邀请码参数以获取对应公司的产品
          const params = { invitation_code: this.invitationCode };
          const result = await get('/api/v1/public/product/products', params, { showErr: false });

          if (result && Array.isArray(result)) {
            // 保存原始产品数据
            this.originalProducts = result;
            // 将产品数据转换为二级分类结构
            this.productCategories = this.convertProductsToCategories(result);
            console.log('产品分类加载成功:', this.productCategories);
            console.log('原始产品数据:', this.originalProducts);
          } else {
            throw new Error('获取产品列表数据格式错误');
          }
        } catch (error) {
          console.error('获取产品列表失败:', error);

          // 根据错误类型显示不同提示
          let errorMessage = '产品列表加载失败';
          if (error.message && error.message.includes('登录')) {
            errorMessage = '请先登录后再试';
          } else if (error.code === 401 || error.code === 403) {
            errorMessage = '权限不足，请联系管理员';
          } else if (error.message && error.message.includes('邀请码')) {
            errorMessage = '邀请码无效，无法获取产品列表';
          }

          // 不使用默认产品分类，保持空状态
          this.originalProducts = [];
          this.productCategories = [];

          uni.showModal({
            title: '产品加载失败',
            content: `${errorMessage}，请检查网络连接或联系管理员`,
            showCancel: true,
            cancelText: '返回',
            confirmText: '重试',
            success: (res) => {
              if (res.confirm) {
                // 重试加载产品
                this.loadProducts();
              } else {
                // 返回上一页
                uni.navigateBack();
              }
            }
          });
        } finally {
          this.productsLoading = false;
        }
      },

      // 将产品数据转换为分类结构
      convertProductsToCategories(products) {
        // 如果API返回的已经是分类结构，直接使用
        if (products.length > 0 && products[0].products) {
          return products;
        }

        // 按照产品的category字段进行分组
        const categoryMap = new Map();

        products.forEach(product => {
          const categoryName = product.category || '其他';
          if (!categoryMap.has(categoryName)) {
            categoryMap.set(categoryName, []);
          }
          // 保存完整的产品对象，包含id和name
          categoryMap.get(categoryName).push({
            id: product.id,
            name: product.name
          });
        });

        // 转换为分类结构
        const categories = [];
        categoryMap.forEach((productList, categoryName) => {
          categories.push({
            name: categoryName,
            products: productList
          });
        });

        // 如果没有产品，使用默认分类
        if (categories.length === 0) {
          return [...this.defaultProductCategories];
        }

        return categories;
      },

      // 显示产品选择器
      showProductPicker() {
        // 首先检查是否有邀请码
        if (!this.invitationCode) {
          console.error('邀请码缺失，当前用户信息:', this.currentUserInfo);
          console.error('邀请模式状态:', this.isInvitationMode);

          // 尝试重新获取用户信息并设置邀请码
          this.getCurrentUserInfo();

          // 如果仍然没有邀请码，显示错误提示
          if (!this.invitationCode) {
            uni.showModal({
              title: '邀请码缺失',
              content: '无法获取有效的邀请码，请重新登录或联系管理员',
              showCancel: true,
              cancelText: '重试',
              confirmText: '返回',
              success: (res) => {
                if (res.cancel) {
                  // 重试获取用户信息
                  this.getCurrentUserInfo();
                } else {
                  uni.navigateBack();
                }
              }
            });
            return;
          }
        }

        if (this.productsLoading) {
          uni.showToast({
            title: '产品列表加载中，请稍候',
            icon: 'none'
          });
          return;
        }

        // 检查是否有真实的产品数据
        if (this.originalProducts.length === 0) {
          uni.showModal({
            title: '产品数据未加载',
            content: '产品列表未正确加载，正在重新获取...',
            showCancel: true,
            cancelText: '重新加载',
            confirmText: '返回',
            success: (res) => {
              if (res.cancel) {
                // 重新获取产品列表
                this.getProductList();
              } else {
                uni.navigateBack();
              }
            }
          });
          return;
        }

        if (this.productCategories.length === 0) {
          uni.showToast({
            title: '产品列表为空，请稍后重试',
            icon: 'none'
          });
          // 重新尝试加载产品列表
          this.loadProducts();
          return;
        }

        this.productPickerShow = true;
      },

      // 显示休息日选择器
      showRestDayPicker() {
        this.restDayPickerShow = true;
      },

      // 显示时间选择器
      showTimePicker() {
        this.timePickerShow = true;
      },

      // 切换分类展开/折叠状态
      toggleCategory(categoryIndex) {
        this.$set(this.productCategories[categoryIndex], 'expanded',
          !this.productCategories[categoryIndex].expanded
        );
      },

      // 判断产品是否已选择
      isProductSelected(product) {
        // 支持产品对象和产品名称字符串两种格式
        const productName = typeof product === 'string' ? product : product.name;
        return this.form.products.includes(productName);
      },

      // 切换产品选择状态
      toggleProduct(product) {
        // 支持产品对象和产品名称字符串两种格式
        const productName = typeof product === 'string' ? product : product.name;
        const productId = typeof product === 'string' ? null : product.id;

        const index = this.form.products.indexOf(productName);
        if (index > -1) {
          // 已选择，则取消选择
          this.form.products.splice(index, 1);
          // 同时从产品ID列表中移除
          if (productId) {
            const idIndex = this.form.productIds.indexOf(productId);
            if (idIndex > -1) {
              this.form.productIds.splice(idIndex, 1);
            }
          }
        } else {
          // 未选择，则添加选择
          this.form.products.push(productName);
          // 同时添加到产品ID列表
          if (productId) {
            this.form.productIds.push(productId);
          }
        }
      },

      // 确认产品选择
      confirmProductSelection() {
        this.productPickerShow = false;
        if (this.form.products.length > 0) {
          uni.showToast({
            title: `已选择${this.form.products.length}项产品`,
            icon: 'success',
            duration: 1500
          });
        }
      },

      // 判断分类是否全选
      isCategoryAllSelected(categoryIndex) {
        const category = this.productCategories[categoryIndex];
        if (!category || !category.products || category.products.length === 0) {
          return false;
        }

        return category.products.every(product => {
          const productName = typeof product === 'string' ? product : product.name;
          return this.form.products.includes(productName);
        });
      },

      // 切换分类批量选择
      toggleCategoryBatch(categoryIndex) {
        const category = this.productCategories[categoryIndex];
        if (!category || !category.products) {
          return;
        }

        const isAllSelected = this.isCategoryAllSelected(categoryIndex);

        if (isAllSelected) {
          // 当前全选，则取消该分类下所有产品
          category.products.forEach(product => {
            const productName = typeof product === 'string' ? product : product.name;
            const productId = typeof product === 'string' ? null : product.id;

            const index = this.form.products.indexOf(productName);
            if (index > -1) {
              this.form.products.splice(index, 1);
            }

            // 同时从产品ID列表中移除
            if (productId) {
              const idIndex = this.form.productIds.indexOf(productId);
              if (idIndex > -1) {
                this.form.productIds.splice(idIndex, 1);
              }
            }
          });

          uni.showToast({
            title: `已取消选择 ${category.name}`,
            icon: 'none',
            duration: 1500
          });
        } else {
          // 当前未全选，则选择该分类下所有产品
          category.products.forEach(product => {
            const productName = typeof product === 'string' ? product : product.name;
            const productId = typeof product === 'string' ? null : product.id;

            if (!this.form.products.includes(productName)) {
              this.form.products.push(productName);
            }

            // 同时添加到产品ID列表
            if (productId && !this.form.productIds.includes(productId)) {
              this.form.productIds.push(productId);
            }
          });

          uni.showToast({
            title: `已选择 ${category.name} 全部产品`,
            icon: 'success',
            duration: 1500
          });
        }
      },

      // 休息日选择确认
      onRestDayConfirm(selectedRestDays) {
        this.form.restDays = selectedRestDays;
        this.restDayPickerShow = false;
      },

      // 开始时间变化
      onStartTimeChange(e) {
        this.startTimeIndex = e.detail.value[0];
        this.form.workStartTime = this.timeOptions[this.startTimeIndex];
      },

      // 结束时间变化
      onEndTimeChange(e) {
        this.endTimeIndex = e.detail.value[0];
        this.form.workEndTime = this.timeOptions[this.endTimeIndex];
      },

      // 确认工作时间
      confirmWorkTime() {
        if (this.startTimeIndex >= this.endTimeIndex) {
          uni.showToast({
            title: '结束时间必须大于开始时间',
            icon: 'none'
          });
          return;
        }
        this.timePickerShow = false;
      },

      // 图片转base64工具方法
      urlToBase64(url) {
        try {
          const imgData = uni.getFileSystemManager().readFileSync(url, 'base64');
          return 'data:image/jpeg;base64,' + imgData;
        } catch (error) {
          console.error('图片转base64失败:', error);
          return '';
        }
      },

      // 上传身份证照片
      uploadIdCard() {
        uni.chooseImage({
          count: 1,
          sizeType: ['original', 'compressed'],
          sourceType: ['album', 'camera'],
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            this.form.idCardImage = tempFilePath;

            // 开始识别流程
            this.recognizeIdCard(tempFilePath);
          },
          fail: () => {
            uni.showToast({
              title: '照片选择失败',
              icon: 'none'
            });
          }
        });
      },

      // 身份证识别主方法
      async recognizeIdCard(filePath) {
        if (this.idCardRecognition.isRecognizing) {
          uni.showToast({
            title: '正在识别中，请稍候',
            icon: 'none'
          });
          return;
        }

        this.idCardRecognition.isRecognizing = true;

        try {
          uni.showLoading({
            title: '图片上传中...'
          });

          // 1. 先上传图片到服务器
          const uploadResult = await this.uploadImageToServer(filePath);

          if (!uploadResult.success) {
            throw new Error(uploadResult.message || '图片上传失败');
          }

          uni.showLoading({
            title: '身份证识别中...'
          });

          // 2. 转换为base64用于OCR识别
          const base64Data = this.urlToBase64(filePath);
          if (!base64Data) {
            throw new Error('图片处理失败');
          }

          // 3. 调用OCR识别（只识别正面）
          this.idCardRecognition.frontBase64 = base64Data;
          await this.recognizeIdCardFront(base64Data);

        } catch (error) {
          console.error('身份证识别失败:', error);
          uni.showToast({
            title: error.message || '识别失败，请重试',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
          this.idCardRecognition.isRecognizing = false;
        }
      },

      // 上传图片到服务器
      uploadImageToServer(filePath) {
        return new Promise((resolve) => {
          uni.uploadFile({
            url: this.$baseUrl + '/api/v1/public/upload',
            filePath: filePath,
            name: 'file',
            dataType: 'json',
            success: (res) => {
              try {
                const result = JSON.parse(res.data);
                if (result.code === 200 && result.data && result.data.url) {
                  // 保存上传后的图片URL到表单数据
                  this.form.idCardImageUrl = result.data.url;
                  console.log('身份证图片上传成功，URL:', result.data.url);
                  resolve({ success: true, url: result.data.url });
                } else {
                  resolve({ success: false, message: '上传失败' });
                }
              } catch (error) {
                resolve({ success: false, message: '响应解析失败' });
              }
            },
            fail: (error) => {
              resolve({ success: false, message: '网络请求失败' });
            }
          });
        });
      },

      // 身份证正面识别
      recognizeIdCardFront(base64Data) {
        return new Promise((resolve, reject) => {
          const requestData = {
            file: base64Data,
            cardTpe: 0  // 0表示正面
          };

          uni.request({
            url: 'https://agentapi.xiaoyujia.com/files/imgsToIdInfo',
            method: 'POST',
            header: {
              'content-type': 'application/json;charset=UTF-8'
            },
            data: JSON.stringify(requestData),
            success: (res) => {
              try {
                const status = res.data.image_status;

                if (status === 'normal') {
                  // 识别成功
                  const wordsResult = res.data.words_result;
                  const recognizedName = wordsResult.姓名?.words || '';
                  const recognizedIdNumber = wordsResult.公民身份号码?.words || '';
                  const recognizedBirth = wordsResult.出生?.words || '';

                  // 保存识别结果
                  this.idCardRecognition.recognizedData.name = recognizedName;
                  this.idCardRecognition.recognizedData.idNumber = recognizedIdNumber;

                  // 自动填充到表单
                  if (recognizedName) {
                    this.form.name = recognizedName;
                  }
                  if (recognizedIdNumber) {
                    this.form.idCard = recognizedIdNumber;
                  }

                  // 处理生日信息
                  if (recognizedBirth && recognizedBirth.length === 8) {
                    try {
                      // 将 19690422 格式转换为 1969-04-22
                      const year = recognizedBirth.substring(0, 4);
                      const month = recognizedBirth.substring(4, 6);
                      const day = recognizedBirth.substring(6, 8);
                      const formattedBirthday = `${year}-${month}-${day}`;

                      // 验证日期有效性
                      const date = new Date(formattedBirthday);
                      if (date.getFullYear() == year && (date.getMonth() + 1) == month && date.getDate() == day) {
                        this.form.birthday = formattedBirthday;
                      }
                    } catch (error) {
                      console.error('生日格式化失败:', error);
                    }
                  }

                  uni.showToast({
                    title: '身份证识别成功',
                    icon: 'success'
                  });

                  resolve({ success: true });

                } else if (status === 'unknown' && res.data.idcard_number_type === 0) {
                  // 部分识别成功
                  const wordsResult = res.data.words_result;
                  const recognizedName = wordsResult.姓名?.words || '';
                  const recognizedIdNumber = wordsResult.公民身份号码?.words || '';
                  const recognizedBirth = wordsResult.出生?.words || '';

                  if (recognizedName) this.form.name = recognizedName;
                  if (recognizedIdNumber) this.form.idCard = recognizedIdNumber;

                  // 处理生日信息
                  if (recognizedBirth && recognizedBirth.length === 8) {
                    try {
                      // 将 19690422 格式转换为 1969-04-22
                      const year = recognizedBirth.substring(0, 4);
                      const month = recognizedBirth.substring(4, 6);
                      const day = recognizedBirth.substring(6, 8);
                      const formattedBirthday = `${year}-${month}-${day}`;

                      // 验证日期有效性
                      const date = new Date(formattedBirthday);
                      if (date.getFullYear() == year && (date.getMonth() + 1) == month && date.getDate() == day) {
                        this.form.birthday = formattedBirthday;
                      }
                    } catch (error) {
                      console.error('生日格式化失败:', error);
                    }
                  }

                  uni.showToast({
                    title: '识别成功，请检查信息',
                    icon: 'none'
                  });

                  resolve({ success: true });

                } else {
                  // 识别失败
                  let errorMessage = '身份证识别失败，请重新上传';

                  if (status === 'reversed_side') {
                    errorMessage = '请上传身份证人像面';
                  } else if (status === 'non_idcard') {
                    errorMessage = '上传的图片中不包含身份证';
                  } else if (status === 'blurred') {
                    errorMessage = '身份证模糊，请重新拍摄';
                  } else if (status === 'other_type_card') {
                    errorMessage = '请上传身份证，不是其他证件';
                  } else if (status === 'over_exposure') {
                    errorMessage = '身份证反光或过曝，请重新拍摄';
                  } else if (status === 'over_dark') {
                    errorMessage = '身份证过暗，请在光线充足处拍摄';
                  }

                  reject(new Error(errorMessage));
                }

              } catch (error) {
                reject(new Error('识别结果解析失败'));
              }
            },
            fail: (error) => {
              reject(new Error('网络请求失败，请检查网络连接'));
            }
          });
        });
      },

      // 身份证号变化时自动提取生日
      onIdCardChange() {
        if (this.form.idCard && this.form.idCard.length === 18) {
          try {
            // 从身份证号提取生日
            const year = this.form.idCard.substring(6, 10);
            const month = this.form.idCard.substring(10, 12);
            const day = this.form.idCard.substring(12, 14);

            // 验证日期有效性
            const birthday = `${year}-${month}-${day}`;
            const date = new Date(birthday);
            if (date.getFullYear() == year && (date.getMonth() + 1) == month && date.getDate() == day) {
              this.form.birthday = birthday;
              uni.showToast({
                title: '已自动识别生日',
                icon: 'success',
                duration: 1500
              });
            }
          } catch (error) {
            console.error('生日提取失败:', error);
          }
        }
      },

      // 生日选择
      onBirthdayChange(e) {
        this.form.birthday = e.detail.value;
      },

      // 选择地址
      selectAddress() {
        // #ifdef MP-WEIXIN
        // 微信小程序使用原生地图选择
        uni.chooseLocation({
          success: (res) => {
            console.log('选择的位置:', res);
            this.form.address = res.address;
            this.form.latitude = res.latitude;
            this.form.longitude = res.longitude;
            uni.showToast({
              title: '地址选择成功',
              icon: 'success'
            });
          },
          fail: (err) => {
            console.error('地址选择失败:', err);
            if (err.errMsg.includes('cancel')) {
              // 用户取消选择
              return;
            }
            uni.showToast({
              title: '地址选择失败，请重试',
              icon: 'none'
            });
          }
        });
        // #endif

        // #ifdef H5
        // H5环境使用自定义地图页面
        uni.navigateTo({
          url: '/pages/h5Map/index',
          events: {
            selectAddress: (data) => {
              console.log('选择的地址:', data);
              if (data && data.address) {
                this.form.address = data.address;
                if (data.latitude && data.longitude) {
                  this.form.latitude = data.latitude;
                  this.form.longitude = data.longitude;
                }
                uni.showToast({
                  title: '地址选择成功',
                  icon: 'success'
                });
              }
            }
          }
        });
        // #endif
      },



      // 表单验证
      validateForm() {
        // 验证必填字段
        if (!this.form.name.trim()) {
          uni.showToast({ title: '请输入姓名', icon: 'none' });
          return false;
        }

        if (!this.form.idCard.trim()) {
          uni.showToast({ title: '请输入身份证号', icon: 'none' });
          return false;
        }

        // 验证身份证格式
        if (!uni.$u.test.idCard(this.form.idCard)) {
          uni.showToast({ title: '身份证号格式不正确', icon: 'none' });
          return false;
        }

        if (!this.form.birthday.trim()) {
          uni.showToast({ title: '请选择生日', icon: 'none' });
          return false;
        }

        if (this.form.products.length === 0) {
          uni.showToast({ title: '请选择服务产品', icon: 'none' });
          return false;
        }

        // 验证产品ID是否有效
        const productIds = this.getProductIds();
        if (productIds.length === 0) {
          uni.showModal({
            title: '产品数据异常',
            content: '无法获取有效的产品ID，请重新选择产品',
            showCancel: true,
            cancelText: '重新选择',
            confirmText: '返回',
            success: (res) => {
              if (res.cancel) {
                this.showProductPicker();
              } else {
                uni.navigateBack();
              }
            }
          });
          return false;
        }

        // 验证产品数据是否为真实数据（非默认数据）
        if (this.originalProducts.length === 0 && this.productCategories.length > 0) {
          uni.showModal({
            title: '产品数据异常',
            content: '产品列表未正确加载，请重新获取产品信息后再提交',
            showCancel: true,
            cancelText: '重新加载',
            confirmText: '返回',
            success: (res) => {
              if (res.cancel) {
                this.loadProducts();
              } else {
                uni.navigateBack();
              }
            }
          });
          return false;
        }

        if (!this.form.phone.trim()) {
          uni.showToast({ title: '请输入手机号', icon: 'none' });
          return false;
        }

        // 验证手机号格式
        if (!uni.$u.test.mobile(this.form.phone)) {
          uni.showToast({ title: '手机号格式不正确', icon: 'none' });
          return false;
        }

        if (!this.form.workStartTime || !this.form.workEndTime) {
          uni.showToast({ title: '请选择接单时间', icon: 'none' });
          return false;
        }



        if (!this.form.address.trim()) {
          uni.showToast({ title: '请输入常住地址', icon: 'none' });
          return false;
        }

        return true;
      },

      // 显示确认对话框
      handleSubmit() {
        if (this.isSubmitting) {
          uni.showToast({
            title: '正在提交中，请勿重复操作',
            icon: 'none'
          });
          return;
        }

        if (!this.validateForm()) {
          return;
        }

        // 显示确认对话框
        this.confirmDialogShow = true;
      },

      // 确认提交
      async confirmSubmit() {
        if (this.isSubmitting) {
          uni.showToast({
            title: '正在提交中，请勿重复操作',
            icon: 'none'
          });
          return;
        }

        this.confirmDialogShow = false;
        this.isSubmitting = true;

        uni.showLoading({
          title: '提交中...'
        });

        try {
          // 构建提交数据
          const submitData = this.buildSubmitData();

          console.log('提交员工入驻数据:', submitData);

          let result;

          if (this.isInvitationMode) {
            // 邀请码模式：无需登录，直接注册（公开接口）
            // 邀请码作为query参数传递，使用JSON格式发送数据
            const apiUrl = `/api/v1/public/product/staff/register-by-invitation?invitation_code=${this.invitationCode}`;
            result = await post(apiUrl, submitData, {
              contentType: 'application/json'
            });
          } else {
            // 普通模式：需要登录
            const token = this.$store.state.token;
            console.log('当前token状态:', token ? `${token.substring(0, 20)}...` : '无token');

            if (!token) {
              throw new Error('用户未登录，请先登录');
            }

            // 调用员工入驻API，传递邀请码参数
            const apiUrl = `/api/v1/product/staff/register${this.invitationCode ? `?invitation_code=${this.invitationCode}` : ''}`;
            result = await post(apiUrl, submitData, {
              contentType: 'application/json'
            });
          }

          uni.hideLoading();
          this.isSubmitting = false;

          // 统一处理两种API的响应格式
          let isSuccess = false;
          let successMessage = '员工入驻成功';
          let staffData = null;

          if (this.isInvitationMode) {
            // 邀请码模式：require.js已经提取了data部分，直接检查是否有staff_id
            isSuccess = result && result.staff_id;
            staffData = result;
            if (result && result.inviter_name && result.store_name) {
              successMessage = `员工入驻成功，欢迎加入${result.store_name}！`;
            }
          } else {
            // 正常模式：require.js已经提取了data部分，直接检查是否有staff_id
            isSuccess = result && result.staff_id;
            staffData = result;
          }

          if (isSuccess) {
            uni.showToast({
              title: successMessage,
              icon: 'success',
              duration: 2000,
              success: () => {
                setTimeout(() => {
                  // 设置全局刷新标识
                  getApp().globalData = getApp().globalData || {};
                  getApp().globalData.needRefreshStaffList = true;

                  // 发送刷新事件通知服务人员列表页面
                  uni.$emit('staffListRefresh');

                  // 检查是否来自订单分享
                  if (this.fromOrderShare && this.orderNumber) {
                    // 跳转回订单分享页面，并传递自动接单参数
                    uni.redirectTo({
                      url: `/pages-public/order-share?orderNumber=${this.orderNumber}&autoAccept=true&mobile=${this.shareUserMobile}&openid=${this.shareUserOpenid}`
                    });
                  } else if (this.isInvitationMode) {
                    // 邀请码模式：跳转到员工端登录页面
                    uni.reLaunch({
                      url: '/pages/login/login?role=staff'
                    });
                  } else {
                    // 正常返回上一页（门店内部操作）
                    uni.navigateBack({
                      success: () => {
                        // 返回成功后再次发送事件，确保列表页面能收到
                        setTimeout(() => {
                          uni.$emit('staffListRefresh');
                        }, 100);
                      }
                    });
                  }
                }, 1500);
              }
            });
          } else {
            throw new Error(result?.msg || result?.message || '员工入驻失败');
          }
        } catch (error) {
          uni.hideLoading();
          this.isSubmitting = false;
          console.error('员工入驻失败:', error);
          console.error('错误类型:', typeof error);
          console.error('错误详情:', JSON.stringify(error, null, 2));

          let errorMessage = '员工入驻失败，请重试';

          // 更详细的错误信息提取
          if (error.message) {
            errorMessage = error.message;
          } else if (error.msg) {
            errorMessage = error.msg;
          } else if (error.data && error.data.msg) {
            errorMessage = error.data.msg;
          } else if (error.errMsg) {
            errorMessage = error.errMsg;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          console.error('最终错误信息:', errorMessage);

          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
      },

      // 取消确认
      cancelConfirm() {
        if (this.isSubmitting) {
          uni.showToast({
            title: '正在提交中，无法取消',
            icon: 'none'
          });
          return;
        }
        this.confirmDialogShow = false;
      },

      // 构建提交数据
      buildSubmitData() {
        // 获取产品ID列表（需要从产品名称转换为ID）
        const productIds = this.getProductIds();

        // 从身份证号推断性别和年龄
        const { sex, age } = this.parseIdCard(this.form.idCard);

        return {
          // 基本信息
          real_name: this.form.name,
          mobile: this.form.phone,
          id_number: this.form.idCard,
          sex: sex,
          age: age,
          avatar: this.form.idCardImageUrl || null,  // 身份证人像页URL（来自上传接口）
          address: this.form.address,
          id_card_address: this.form.address,  // 身份证地址（暂时使用常住地址）

          // 扩展信息（可选）
          birthday: this.form.birthday || null,
          start_time: this.getWorkStartTimeNumber(),
          end_time: this.getWorkEndTimeNumber(),
          rest_days: this.getRestDaysString(),
          native_place: null,
          nation: null,
          marriage_status: "0", // 默认未婚
          education_background: "0", // 默认小学
          height: "0",
          weight: "0",
          family_address: this.form.address,
          medical_history: null,

          // 银行卡信息（可选）
          bank_name: null,
          bank_card_holder: null,
          bank_card_number: null,
          bank_card_photo: null,

          // 紧急联系人信息（可选）
          emergency_contact_name: null,
          emergency_contact_relation: "0",
          emergency_contact_phone: null,
          emergency_contact_address: null,

          // 工作相关
          travel_tool: null,

          // 地理位置信息
          lng: this.form.longitude ? parseFloat(this.form.longitude) : null,
          lat: this.form.latitude ? parseFloat(this.form.latitude) : null,
          province_id: null,
          province_name: null,
          area_id: "0",
          area_name: null,
          address_desc: this.form.doorNumber || null,

          // 产品关联
          product_ids: productIds
        };
      },

      // 从身份证号解析性别和年龄
      parseIdCard(idCard) {
        if (!idCard || idCard.length !== 18) {
          return { sex: "1", age: "25" }; // 默认值
        }

        try {
          // 获取出生年份
          const birthYear = parseInt(idCard.substring(6, 10));
          const currentYear = new Date().getFullYear();
          const age = currentYear - birthYear;

          // 获取性别（倒数第二位，奇数为男，偶数为女）
          const sexCode = parseInt(idCard.substring(16, 17));
          const sex = sexCode % 2 === 1 ? "1" : "2"; // 1-男，2-女

          return { sex, age: age.toString() };
        } catch (error) {
          console.error('身份证解析失败:', error);
          return { sex: "1", age: "25" }; // 默认值
        }
      },

      // 获取工作开始时间数字
      getWorkStartTimeNumber() {
        if (!this.form.workStartTime) return 8;
        // 从"8时"中提取数字8
        const match = this.form.workStartTime.match(/(\d+)/);
        return match ? parseInt(match[1]) : 8;
      },

      // 获取工作结束时间数字
      getWorkEndTimeNumber() {
        if (!this.form.workEndTime) return 18;
        // 从"18时"中提取数字18
        const match = this.form.workEndTime.match(/(\d+)/);
        return match ? parseInt(match[1]) : 18;
      },

      // 获取休息日字符串
      getRestDaysString() {
        if (!this.form.restDays || this.form.restDays.length === 0) return "";

        // 将中文星期转换为数字：周一=1, 周二=2, ..., 周日=7
        const dayMap = {
          '周一': '1', '周二': '2', '周三': '3', '周四': '4',
          '周五': '5', '周六': '6', '周日': '7'
        };

        const dayNumbers = this.form.restDays.map(day => dayMap[day]).filter(Boolean);
        return dayNumbers.join(',');
      },

      // 获取产品ID列表
      getProductIds() {
        // 如果有保存的产品ID，直接使用
        if (this.form.productIds && this.form.productIds.length > 0) {
          console.log('使用已保存的产品ID:', this.form.productIds);
          return this.form.productIds.map(id => parseInt(id));
        }

        // 如果没有产品ID，但有产品名称，尝试从原始产品数据中查找ID
        if (this.form.products.length > 0) {
          const productIds = [];

          // 从原始产品数据中查找匹配的产品ID
          this.form.products.forEach(productName => {
            const foundProduct = this.originalProducts.find(product => product.name === productName);
            if (foundProduct && foundProduct.id) {
              productIds.push(parseInt(foundProduct.id));
            } else {
              // 如果在原始数据中找不到，尝试从分类数据中查找
              this.productCategories.forEach(category => {
                if (category.products) {
                  const categoryProduct = category.products.find(product =>
                    (product.name || product) === productName
                  );
                  if (categoryProduct && categoryProduct.id) {
                    productIds.push(parseInt(categoryProduct.id));
                  }
                }
              });
            }
          });

          console.log('从产品名称匹配的ID:', productIds);
          return productIds.length > 0 ? productIds : [];
        }

        console.log('未选择任何产品，返回空数组');
        return []; // 返回空数组，要求用户必须选择产品
      },

      // loadProducts别名，指向getProductList方法
      loadProducts() {
        return this.getProductList();
      }
    },
  };
</script>

<style lang="scss" scoped>
  // 自定义返回按钮样式
  .custom-back-btn {
    display: flex;
    align-items: center;
    padding: 10rpx;
    cursor: pointer;
    transition: opacity 0.3s ease;

    &:active {
      opacity: 0.7;
    }
  }

  .page {
    min-height: 100vh;
    background: linear-gradient(180deg, #fdd118 0%, #fff3a0 30%, #f5f7fb 100%);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
  }

  .invitation-banner {
    margin: 20rpx;
    margin-top: 120rpx;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16rpx;
    padding: 24rpx;
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }

  .invitation-content {
    display: flex;
    align-items: center;
    gap: 12rpx;
  }

  .invitation-text {
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
  }

  .inviter-name {
    font-weight: 600;
    color: #1890ff;
  }

  .store-name {
    font-weight: 600;
    color: #52c41a;
  }

  // 装饰性背景元素
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    pointer-events: none;
    z-index: 0;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.decoration-circle-1 {
        width: 200rpx;
        height: 200rpx;
        top: 200rpx;
        right: -100rpx;
        animation: float 6s ease-in-out infinite;
      }

      &.decoration-circle-2 {
        width: 150rpx;
        height: 150rpx;
        top: 400rpx;
        left: -75rpx;
        animation: float 8s ease-in-out infinite reverse;
      }
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  .leftImg {
    display: block;
    margin-right: 28rpx;
    width: 36rpx;
    height: 36rpx;
  }

  .form-container {
    flex: 1;
    padding: 40rpx 0 120rpx;
    position: relative;
    z-index: 1;
  }

  .form-section {
    padding: 0 20rpx;
  }

  .form-group {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 0 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10rpx);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2rpx;
      background: linear-gradient(90deg, #fdd118, #ff801c);
    }
  }

  .form-item {
    padding: 35rpx 0;
    border-bottom: 1rpx solid rgba(238, 238, 238, 0.6);
    position: relative;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: rgba(253, 209, 24, 0.02);
    }

    .input {
      width: 100%;
      height: 80rpx;
      font-size: 28rpx;
      color: #333333;
      background-color: rgba(245, 245, 245, 0.5);
      border-radius: 12rpx;
      padding: 0 20rpx;
      border: 1rpx solid transparent;
      transition: all 0.3s ease;

      &:focus {
        background-color: rgba(253, 209, 24, 0.1);
        border-color: #fdd118;
        box-shadow: 0 0 0 2rpx rgba(253, 209, 24, 0.2);
      }
    }

    // 邀请码输入框样式（与其他表单字段保持一致）
    .invitation-code-input {
      display: flex;
      align-items: center;
      width: 100%;
      height: 80rpx;
      background-color: rgba(245, 245, 245, 0.5);
      border-radius: 12rpx;
      padding: 0 20rpx;
      border: 1rpx solid transparent;
      transition: all 0.3s ease;
      gap: 12rpx;

      // 与input字段相同的hover效果
      &:hover {
        background-color: rgba(253, 209, 24, 0.02);
      }
    }

    .invitation-code-value {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      font-weight: 600;
      font-family: 'Courier New', monospace;
      letter-spacing: 1rpx;
    }

    .copy-code-btn {
      display: flex;
      align-items: center;
      gap: 6rpx;
      padding: 8rpx 16rpx;
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      font-size: 24rpx;
      border-radius: 8rpx;
      border: 1rpx solid rgba(24, 144, 255, 0.2);
      flex-shrink: 0;
      transition: all 0.3s ease;

      &:active {
        background: rgba(24, 144, 255, 0.2);
        transform: scale(0.95);
      }

      &::after {
        border: none;
      }

      text {
        font-size: 24rpx;
        font-weight: 500;
      }
    }

    .textarea {
      width: 100%;
      height: 160rpx;
      font-size: 28rpx;
      color: #333333;
      background-color: rgba(245, 245, 245, 0.5);
      border-radius: 12rpx;
      padding: 20rpx;
      border: 1rpx solid transparent;
      transition: all 0.3s ease;

      &:focus {
        background-color: rgba(253, 209, 24, 0.1);
        border-color: #fdd118;
        box-shadow: 0 0 0 2rpx rgba(253, 209, 24, 0.2);
      }
    }

    &.date-picker {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .verify-btn {
      width: 30%;
      color: #fdd118;
      font-size: 28rpx;
    }
  }

  // 标签样式优化
  .label-wrapper {
    margin-bottom: 20rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .wechat-phone-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    background: #f8f9fa;
    border: 1rpx solid #07C160;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: #07C160;
    font-weight: 500;
    transition: all 0.3s ease;

    &:active {
      background: #e8f5e8;
      transform: scale(0.95);
    }

    &::after {
      border: none;
    }
  }

  .label {
    font-size: 30rpx;
    color: #333333;
    display: block;
    font-weight: 500;
    position: relative;

    &.required {
      &::before {
        content: '*';
        color: #ff4757;
        font-size: 30rpx;
        margin-right: 8rpx;
        font-weight: bold;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -8rpx;
        left: 0;
        width: 30rpx;
        height: 2rpx;
        background: linear-gradient(90deg, #fdd118, #ff801c);
        border-radius: 1rpx;
      }
    }
  }

  // 字段说明样式
  .field-description {
    display: block;
    font-size: 24rpx;
    color: #999999;
    margin-top: 12rpx;
    line-height: 1.4;
    padding-left: 4rpx;
  }

  .skill-selector,
  .rest-day-selector,
  .time-selector,
  .address-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 24rpx;
    background-color: rgba(245, 245, 245, 0.5);
    border-radius: 12rpx;
    border: 1rpx solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background-color: rgba(253, 209, 24, 0.1);
      border-color: rgba(253, 209, 24, 0.3);
    }

    .selector-text {
      font-size: 28rpx;
      color: #333333;
      flex: 1;
      margin: 0 12rpx;

      &.placeholder {
        color: #999999;
      }
    }
  }

  .upload-section {
    padding: 20rpx 0;
    display: flex;
    justify-content: center;

    .upload-area {
      width: 480rpx;
      height: 280rpx;
      background: linear-gradient(135deg, rgba(253, 209, 24, 0.1), rgba(255, 128, 28, 0.1));
      border-radius: 16rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border: 2rpx dashed rgba(253, 209, 24, 0.4);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;

      &:hover {
        border-color: #fdd118;
        background: linear-gradient(135deg, rgba(253, 209, 24, 0.15), rgba(255, 128, 28, 0.15));
        transform: translateY(-2rpx);
        box-shadow: 0 8rpx 20rpx rgba(253, 209, 24, 0.2);
      }

      .uploaded-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 14rpx;
      }

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12rpx;

        .upload-text {
          font-size: 26rpx;
          color: #666666;
          text-align: center;
          font-weight: 500;
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        width: 20rpx;
        height: 20rpx;
        background: linear-gradient(45deg, #fdd118, #ff801c);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }
    }
  }

  // 时间选择器弹窗样式
  .time-picker-container {
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;

    .picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #eeeeee;

      .cancel-btn {
        font-size: 28rpx;
        color: #666666;
      }

      .title {
        font-size: 32rpx;
        color: #333333;
        font-weight: 500;
      }

      .confirm-btn {
        font-size: 28rpx;
        color: #fdd118;
      }
    }

    .picker-content {
      padding: 20rpx;

      .time-range-picker {
        display: flex;
        gap: 40rpx;

        .time-column {
          flex: 1;

          .column-title {
            display: block;
            text-align: center;
            font-size: 26rpx;
            color: #666666;
            margin-bottom: 20rpx;
          }

          .time-picker-view {
            height: 400rpx;

            .time-item {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 80rpx;
              font-size: 28rpx;
              color: #333333;
            }
          }
        }
      }
    }
  }

  .tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .tag {
      padding: 12rpx 30rpx;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #666666;

      &.active {
        background-color: rgba(9, 190, 137, 0.1);
        color: #fdd118;
        border: 1rpx solid #fdd118;
      }
    }
  }



  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx 30rpx;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20rpx);
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid rgba(255, 255, 255, 0.8);
    z-index: 10;



    .submit-btn {
      width: 100%;
      height: 96rpx;
      line-height: 96rpx;
      text-align: center;
      background: linear-gradient(135deg, #fdd118, #ff801c);
      color: #ffffff;
      font-size: 34rpx;
      border-radius: 48rpx;
      border: none;
      font-weight: 600;
      box-shadow: 0 8rpx 24rpx rgba(253, 209, 24, 0.4);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
      }

      &:hover::before {
        left: 100%;
      }

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);
      }

      &:hover {
        box-shadow: 0 12rpx 32rpx rgba(253, 209, 24, 0.5);
        transform: translateY(-2rpx);
      }

      &[disabled] {
        background: #cccccc !important;
        color: #999999 !important;
        box-shadow: none !important;
        transform: none !important;
        opacity: 0.6;

        &::before {
          display: none;
        }

        &:hover {
          transform: none !important;
          box-shadow: none !important;
        }

        &:active {
          transform: none !important;
          box-shadow: none !important;
        }
      }
    }
  }

  /* 产品选择器样式 */
  .product-picker-container {
    background: white;
    border-radius: 20rpx 20rpx 0 0;
    padding-bottom: env(safe-area-inset-bottom);
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;
    background: white;
    position: sticky;
    top: 0;
    z-index: 10;

    .cancel-btn, .confirm-btn {
      font-size: 32rpx;
      color: #1890ff;
      padding: 10rpx 20rpx;
    }

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .picker-content {
    flex: 1;
    overflow: hidden;
  }

  .product-scroll-container {
    height: 60vh;
    padding: 0 30rpx;
  }

  .product-category {
    margin-bottom: 20rpx;
  }

  .category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: #fafafa;
    border-radius: 10rpx;
    margin-bottom: 16rpx;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    .category-left {
      display: flex;
      align-items: center;
      flex: 1;

      .category-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-right: 12rpx;
      }

      .category-count {
        font-size: 24rpx;
        color: #999;
      }
    }

    .category-actions {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .batch-select-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 8rpx 16rpx;
        background: #fff;
        border-radius: 20rpx;
        border: 1rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        &.selected {
          background: rgba(24, 144, 255, 0.1);
          border-color: #1890ff;
        }

        .batch-text {
          font-size: 24rpx;
          color: #999;
          transition: color 0.3s ease;

          &.active {
            color: #1890ff;
            font-weight: 500;
          }
        }
      }

      .expand-btn {
        padding: 8rpx;
        border-radius: 50%;
        transition: background 0.3s ease;

        &:active {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }

  .products-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    padding: 16rpx 0;
  }

  .product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16rpx 24rpx;
    background: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 30rpx;
    min-width: 140rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }

    &.active {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
      box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
    }
  }

  .product-text {
    font-size: 28rpx;
    color: #666;
    margin-right: 8rpx;
  }

  .product-item.active .product-text {
    color: #1890ff;
    font-weight: 500;
  }

  .selected-products-preview {
    padding: 20rpx 30rpx;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    min-height: 80rpx;
    position: sticky;
    bottom: 0;
  }

  .preview-label {
    font-size: 28rpx;
    color: #666;
    margin-right: 16rpx;
    flex-shrink: 0;
  }

  .preview-text {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    line-height: 1.4;
  }

  /* 时间选择器样式 */
  .time-picker-container {
    background: white;
    border-radius: 20rpx 20rpx 0 0;
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 确认对话框样式 */
  .confirm-dialog {
    width: 640rpx;
    max-height: 80vh;
    background: white;
    border-radius: 20rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .dialog-header {
    padding: 40rpx 30rpx 20rpx;
    text-align: center;
    background: linear-gradient(135deg, #fdd118, #ff801c);
    color: white;
    position: relative;
    border-radius: 20rpx 20rpx 0 0;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2rpx;
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .dialog-title {
    font-size: 36rpx;
    font-weight: 600;
    display: block;
    margin-bottom: 12rpx;
  }

  .dialog-subtitle {
    font-size: 26rpx;
    opacity: 0.9;
    display: block;
  }

  .dialog-content {
    flex: 1;
    max-height: 50vh;
    padding: 0 30rpx;
  }

  .info-section {
    padding: 20rpx 0;
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }

  .info-label {
    font-size: 28rpx;
    color: #666;
    width: 160rpx;
    flex-shrink: 0;
    font-weight: 500;
  }

  .info-value {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    line-height: 1.5;
    word-break: break-all;
  }

  .dialog-footer {
    display: flex;
    padding: 30rpx;
    gap: 20rpx;
    background: #fafafa;
    border-top: 1rpx solid #f0f0f0;
  }

  .dialog-footer .cancel-btn {
    flex: 1;
    height: 80rpx;
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 12rpx;
    font-size: 30rpx;
    font-weight: 500;
    transition: all 0.3s ease;

    &:active {
      background: #e8e8e8;
      transform: scale(0.98);
    }

    &[disabled] {
      background: #f0f0f0 !important;
      color: #ccc !important;
      transform: none !important;
      opacity: 0.6;
    }
  }

  .dialog-footer .confirm-btn {
    flex: 1;
    height: 80rpx;
    background: linear-gradient(135deg, #fdd118, #ff801c);
    color: white;
    border: none;
    border-radius: 12rpx;
    font-size: 30rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);

    &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
    }

    &[disabled] {
      background: #cccccc !important;
      color: #999999 !important;
      box-shadow: none !important;
      transform: none !important;
      opacity: 0.6;
    }
  }
</style>