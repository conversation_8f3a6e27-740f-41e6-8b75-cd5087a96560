<template>
  <view class="page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 头部 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <view class="navbar">
          <view class="nav-left" @click="goBack">
            <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
          </view>
          <text class="nav-title">销售项目</text>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalProducts }}</text>
          <text class="stats-label">总项目数</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ totalCategories }}</text>
          <text class="stats-label">服务分类</text>
        </view>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="category-section" v-if="categoryList.length > 0">
      <scroll-view class="category-scroll" scroll-x>
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: selectedCategory === '全部' }"
            @click="handleCategoryChange('全部')"
          >
            <u-icon name="grid" size="16" :color="selectedCategory === '全部' ? '#fdd118' : '#999'"></u-icon>
            <text>全部</text>
          </view>
          <view
            class="category-item"
            :class="{ active: selectedCategory === category }"
            @click="handleCategoryChange(category)"
            v-for="category in categoryList"
            :key="category"
          >
            <u-icon name="grid" size="16" :color="selectedCategory === category ? '#fdd118' : '#999'"></u-icon>
            <text>{{ category }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 产品列表 -->
    <scroll-view
      class="product-list"
      scroll-y
      :style="{ height: listHeight }"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <u-loading-icon mode="circle" color="#fdd118" size="24"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-else-if="filteredProductList.length === 0">
        <image src="/static/img/empty.png" class="empty-image" mode="aspectFit"></image>
        <text class="empty-text">暂无销售项目</text>
      </view>

      <!-- 产品卡片列表 -->
      <view class="product-grid" v-else>
        <view
          class="product-card"
          v-for="product in filteredProductList"
          :key="product.id"
          @click="viewProductDetail(product)"
        >
          <view class="product-image-container">
            <image
              class="product-image"
              :src="getProductImage(product)"
              mode="aspectFill"
              @error="handleImageError"
            ></image>
          </view>

          <view class="product-info">
            <view class="product-name">{{ product.product_name || product.name }}</view>
            <view class="product-category">{{ product.service_skill_name || product.service_skill_main_name || '其他服务' }}</view>

            <view class="product-price" v-if="product.sku_info && product.sku_info.now_price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{ product.sku_info.now_price }}</text>
              <text class="price-unit">{{ product.sku_info.type_price_unit || '/次' }}</text>
            </view>

            <view class="product-status">
              <view class="status-tag online">可销售</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getStaffCompanyProducts } from '@/api/staff.js'

export default {
  name: 'SalesProjects',
  data() {
    return {
      loading: false,
      refreshing: false, // 下拉刷新状态
      productList: [], // 原始产品列表
      filteredProductList: [], // 过滤后的产品列表
      selectedCategory: '全部', // 当前选中的分类
      categoryList: [], // 分类列表
      listHeight: 'calc(100vh - 400rpx)' // 列表高度
    }
  },
  computed: {
    ...mapState(['StatusBar', 'staffInfo']),
    ...mapGetters(['getCurrentSelectedCompany']),

    // 总产品数
    totalProducts() {
      return this.productList.length
    },

    // 总分类数
    totalCategories() {
      return this.categoryList.length
    }
  },
  onLoad() {
    this.loadProductList()
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 加载产品列表
    async loadProductList() {
      try {
        this.loading = true

        // 检查当前员工信息
        const staffInfo = this.$store.state.staffInfo
        if (!staffInfo || !staffInfo.mobile) {
          throw new Error('员工信息不存在，请重新登录')
        }

        // 获取当前选中的公司信息
        const selectedCompany = this.$store.getters.getCurrentSelectedCompany
        if (!selectedCompany) {
          throw new Error('请先选择公司')
        }

        console.log('当前员工:', staffInfo.mobile, '选中公司:', selectedCompany.company_id || selectedCompany.id)

        // 调用员工端专用API获取产品列表
        const result = await getStaffCompanyProducts(selectedCompany.company_id || selectedCompany.id)

        // 处理返回的分类数据结构
        if (result && result.categories) {
          this.processCategorizedData(result.categories)
        } else {
          this.productList = []
          this.filteredProductList = []
          this.categoryList = []
        }

        console.log('员工端销售项目加载成功:', this.productList)
      } catch (error) {
        console.error('加载销售项目失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 处理分类数据
    processCategorizedData(categories) {
      const allProducts = []
      const categorySet = new Set()

      // 展开所有分类的产品
      categories.forEach(category => {
        if (category.products && Array.isArray(category.products)) {
          category.products.forEach(product => {
            // 为产品添加分类信息
            product.service_skill_name = category.name
            allProducts.push(product)
          })
          categorySet.add(category.name)
        }
      })

      this.productList = allProducts
      this.categoryList = Array.from(categorySet)
      this.updateFilteredProducts()
    },

    // 更新过滤后的产品列表
    updateFilteredProducts() {
      if (this.selectedCategory === '全部') {
        this.filteredProductList = this.productList
      } else {
        this.filteredProductList = this.productList.filter(product => {
          const categoryName = product.service_skill_name || product.service_skill_main_name || '其他服务'
          return categoryName === this.selectedCategory
        })
      }
    },

    // 处理分类切换
    handleCategoryChange(category) {
      this.selectedCategory = category
      this.updateFilteredProducts()
    },

    // 获取产品图片
    getProductImage(product) {
      // 优先使用后端返回的完整图片URL
      if (product.cover_image_url) {
        return product.cover_image_url
      }
      // 如果有图片列表，使用第一张图片
      if (product.images && product.images.length > 0) {
        return product.images[0].url
      }
      // 如果有图片ID，构建图片URL
      if (product.img_id) {
        return `/api/v1/common/file/download/${product.img_id}`
      }
      // 返回默认图片
      return '/static/logo.png'
    },

    // 处理图片加载错误
    handleImageError(e) {
      e.target.src = '/static/logo.png'
    },

    // 查看产品详情
    viewProductDetail(product) {
      // 显示产品详情信息
      const productName = product.product_name || product.name
      const category = product.service_skill_name || product.service_skill_main_name || '其他服务'
      let content = `产品名称：${productName}\n服务分类：${category}`

      if (product.sku_info && product.sku_info.now_price) {
        content += `\n价格：¥${product.sku_info.now_price}${product.sku_info.type_price_unit || '/次'}`
      }

      uni.showModal({
        title: '产品详情',
        content: content,
        showCancel: false,
        confirmText: '知道了'
      })
    },

    // 下拉刷新
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadProductList()
      } finally {
        this.refreshing = false
      }
    }
  }
}
</script>

<style scoped>
.page {
  background: #f5f7fb;
  min-height: 100vh;
}

/* 头部样式 */
.header-section {
  position: relative;
  height: calc(180rpx + var(--status-bar-height));
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #fdd118 0%, #f5a623 100%);
}

.header-content {
  position: relative;
  z-index: 2;
  padding-top: var(--status-bar-height);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 180rpx;
  padding: 0 32rpx;
}

.nav-left, .nav-right {
  width: 80rpx;
  display: flex;
  justify-content: center;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

/* 统计信息样式 */
.stats-section {
  margin: -60rpx 24rpx 24rpx;
  position: relative;
  z-index: 3;
}

.stats-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #fdd118;
  line-height: 1;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 分类筛选样式 */
.category-section {
  margin: 0 24rpx 24rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #fff9e6;
  border: 2rpx solid #fdd118;
}

.category-item text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.category-item.active text {
  color: #fdd118;
  font-weight: 500;
}

/* 产品列表样式 */
.product-list {
  margin: 0 24rpx;
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text, .empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
}

.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  padding-bottom: 40rpx;
}

.product-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.product-card:active {
  transform: scale(0.98);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 240rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 24rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.product-category {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4757;
  font-weight: 500;
}

.price-value {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

.product-status {
  display: flex;
  justify-content: flex-end;
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-tag.online {
  background: #e8f5e8;
  color: #52c41a;
}
</style>
