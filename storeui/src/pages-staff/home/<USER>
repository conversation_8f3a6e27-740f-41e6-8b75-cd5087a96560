<template>
  <view class="page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 头部 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <view class="navbar">
          <view class="nav-left" @click="goBack">
            <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
          </view>
          <text class="nav-title">销售项目</text>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalProducts }}</text>
          <text class="stats-label">总项目数</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ totalCategories }}</text>
          <text class="stats-label">服务分类</text>
        </view>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="category-section" v-if="categoryList.length > 0">
      <scroll-view class="category-scroll" scroll-x>
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: selectedCategory === '全部' }"
            @click="handleCategoryChange('全部')"
          >
            <u-icon name="grid" size="16" :color="selectedCategory === '全部' ? '#fdd118' : '#999'"></u-icon>
            <text>全部</text>
          </view>
          <view
            class="category-item"
            :class="{ active: selectedCategory === category }"
            @click="handleCategoryChange(category)"
            v-for="category in categoryList"
            :key="category"
          >
            <u-icon name="grid" size="16" :color="selectedCategory === category ? '#fdd118' : '#999'"></u-icon>
            <text>{{ category }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 产品列表 -->
    <scroll-view
      class="product-list"
      scroll-y
      :style="{ height: listHeight }"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <u-loading-icon mode="circle" color="#fdd118" size="24"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-else-if="!loading && filteredProductList.length === 0">
        <u-empty text="暂无销售项目" mode="list"></u-empty>
      </view>

      <!-- 瀑布流产品列表 -->
      <view class="waterfall-container" v-else>
        <view class="waterfall-column" v-for="(column, columnIndex) in waterfallColumns" :key="`column-${columnIndex}`">
          <view
            class="waterfall-item"
            v-for="(product, productIndex) in column"
            :key="`item-${columnIndex}-${productIndex}-${product.id || product.uuid || productIndex}`"
            @click="viewProductDetail(product)"
          >
            <!-- 移除图片显示 -->
            <view class="product-content">
              <view class="product-name">{{ product.product_name || product.name }}</view>
              <view class="product-category">{{ product.service_skill_name || product.service_skill_main_name || '其他服务' }}</view>
              <view class="product-price" v-if="product.sku_info && product.sku_info.now_price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ product.sku_info.now_price }}</text>
                <text class="price-unit">{{ product.sku_info.type_price_unit || '/次' }}</text>
              </view>
              <view class="product-tags">
                <text class="tag">可销售</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getStaffCompanyProducts } from '@/api/staff.js'

// 数据管理模块
const DataModule = {
  // 数据初始化
  initData() {
    return {
      loading: false,
      refreshing: false,
      productList: [],
      filteredProductList: [],
      selectedCategory: '全部',
      categoryList: [],
      listHeight: 'calc(100vh - 400rpx)'
    }
  },

  // 处理分类数据
  processCategorizedData(categories) {
    const allProducts = []
    const categorySet = new Set()

    categories.forEach(category => {
      if (category.products && Array.isArray(category.products)) {
        category.products.forEach(product => {
          product.service_skill_name = category.name
          allProducts.push(product)
        })
        categorySet.add(category.name)
      }
    })

    return {
      products: allProducts,
      categories: Array.from(categorySet)
    }
  },

  // 更新过滤后的产品列表
  updateFilteredProducts(productList, selectedCategory) {
    if (selectedCategory === '全部') {
      return productList
    } else {
      return productList.filter(product => {
        const categoryName = product.service_skill_name || product.service_skill_main_name || '其他服务'
        return categoryName === selectedCategory
      })
    }
  },

  // 计算瀑布流布局（优化性能版本）
  calculateWaterfallLayout(productList, columnCount = 2) {
    if (!productList || productList.length === 0) {
      return Array.from({ length: columnCount }, () => [])
    }

    const columns = Array.from({ length: columnCount }, () => [])
    const columnHeights = new Array(columnCount).fill(0)

    // 简化高度计算，提升性能
    productList.forEach((product) => {
      // 固定高度估算，避免复杂计算
      const baseHeight = 180
      const hasPrice = product.sku_info && product.sku_info.now_price
      const estimatedHeight = baseHeight + (hasPrice ? 40 : 0)

      // 找到最短的列
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights))

      // 将产品添加到最短的列
      columns[shortestColumnIndex].push(product)
      columnHeights[shortestColumnIndex] += estimatedHeight
    })

    return columns
  }
}

// 图片处理模块已移除，不再显示商品图片

// API调用模块
const ApiModule = {
  // 缓存数据
  _cache: new Map(),
  _cacheTimeout: 5 * 60 * 1000, // 5分钟缓存

  // 获取缓存key
  getCacheKey(companyId) {
    return `staff_products_${companyId}`
  },

  // 检查缓存是否有效
  isCacheValid(cacheKey) {
    const cached = this._cache.get(cacheKey)
    if (!cached) return false
    return Date.now() - cached.timestamp < this._cacheTimeout
  },

  // 加载产品列表
  async loadProductList(context) {
    try {
      context.loading = true

      // 检查当前员工信息
      const staffInfo = context.$store.state.staffInfo
      if (!staffInfo || !staffInfo.mobile) {
        throw new Error('员工信息不存在，请重新登录')
      }

      // 获取当前选中的公司信息
      const selectedCompany = context.$store.getters.getCurrentSelectedCompany
      if (!selectedCompany) {
        throw new Error('请先选择公司')
      }

      const companyId = selectedCompany.company_id || selectedCompany.id
      const cacheKey = this.getCacheKey(companyId)

      // 检查缓存
      if (this.isCacheValid(cacheKey)) {
        const cached = this._cache.get(cacheKey)
        const processedData = DataModule.processCategorizedData(cached.data.categories)
        context.productList = processedData.products
        context.categoryList = processedData.categories
        context.updateFilteredProducts()
        console.log('使用缓存数据，产品数量:', context.productList.length)
        return
      }

      console.log('当前员工:', staffInfo.mobile, '选中公司:', companyId)

      // 性能监控：记录API调用开始时间
      const apiStartTime = Date.now()

      // 调用员工端专用API获取产品列表
      const result = await getStaffCompanyProducts(companyId)

      // 性能监控：记录API调用耗时
      const apiDuration = Date.now() - apiStartTime
      console.log(`API调用耗时: ${apiDuration}ms`)

      // 处理返回的分类数据结构
      if (result && result.categories) {
        // 缓存数据
        this._cache.set(cacheKey, {
          data: result,
          timestamp: Date.now()
        })

        const processedData = DataModule.processCategorizedData(result.categories)
        context.productList = processedData.products
        context.categoryList = processedData.categories
        context.updateFilteredProducts()
      } else {
        context.productList = []
        context.filteredProductList = []
        context.categoryList = []
      }

      console.log('员工端销售项目加载成功:', context.productList.length, '个产品')
    } catch (error) {
      console.error('加载销售项目失败:', error)
      uni.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
    } finally {
      context.loading = false
    }
  },

  // 下拉刷新
  async onRefresh(context) {
    context.refreshing = true
    try {
      await this.loadProductList(context)
    } finally {
      context.refreshing = false
    }
  }
}

// 交互处理模块
const InteractionModule = {
  // 返回上一页
  goBack() {
    uni.navigateBack()
  },

  // 处理分类切换
  handleCategoryChange(context, category) {
    context.selectedCategory = category
    context.updateFilteredProducts()
  },

  // 查看产品详情
  viewProductDetail(product) {
    const productName = product.product_name || product.name
    const category = product.service_skill_name || product.service_skill_main_name || '其他服务'
    let content = `产品名称：${productName}\n服务分类：${category}`

    if (product.sku_info && product.sku_info.now_price) {
      content += `\n价格：¥${product.sku_info.now_price}${product.sku_info.type_price_unit || '/次'}`
    }

    uni.showModal({
      title: '产品详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  }
}

export default {
  name: 'SalesProjects',
  data() {
    return DataModule.initData()
  },
  computed: {
    ...mapState(['StatusBar', 'staffInfo']),
    ...mapGetters(['getCurrentSelectedCompany']),

    // 总产品数
    totalProducts() {
      return this.productList.length
    },

    // 总分类数
    totalCategories() {
      return this.categoryList.length
    },

    // 瀑布流列数据
    waterfallColumns() {
      return DataModule.calculateWaterfallLayout(this.filteredProductList, 2)
    }
  },
  onLoad() {
    ApiModule.loadProductList(this)
  },
  methods: {
    // 导航方法
    goBack: InteractionModule.goBack,

    // 数据加载方法
    async loadProductList() {
      await ApiModule.loadProductList(this)
    },

    // 数据处理方法
    updateFilteredProducts() {
      this.filteredProductList = DataModule.updateFilteredProducts(this.productList, this.selectedCategory)
    },

    // 交互处理方法
    handleCategoryChange(category) {
      InteractionModule.handleCategoryChange(this, category)
    },

    viewProductDetail(product) {
      InteractionModule.viewProductDetail(product)
    },

    // 图片处理方法已移除

    // 刷新方法
    async onRefresh() {
      await ApiModule.onRefresh(this)
    },

    // 图片加载事件已移除
  }
}
</script>

<style scoped>
.page {
  background: #f5f7fb;
  min-height: 100vh;
}

/* 头部样式 */
.header-section {
  position: relative;
  height: calc(180rpx + var(--status-bar-height));
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #fdd118 0%, #f5a623 100%);
}

.header-content {
  position: relative;
  z-index: 2;
  padding-top: var(--status-bar-height);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 180rpx;
  padding: 0 32rpx;
}

.nav-left, .nav-right {
  width: 80rpx;
  display: flex;
  justify-content: center;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

/* 统计信息样式 */
.stats-section {
  margin: -60rpx 24rpx 24rpx;
  position: relative;
  z-index: 3;
}

.stats-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #fdd118;
  line-height: 1;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 分类筛选样式 */
.category-section {
  margin: 0 24rpx 24rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #fff9e6;
  border: 2rpx solid #fdd118;
}

.category-item text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.category-item.active text {
  color: #fdd118;
  font-weight: 500;
}

/* 瀑布流容器样式 */
.waterfall-container {
  display: flex;
  padding: 0 20rpx;
  gap: 20rpx;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.waterfall-item {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.waterfall-item:active {
  transform: scale(0.98);
}

/* 移除图片相关样式 */

.product-content {
  padding: 24rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: 500;
}

.price-value {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 600;
  margin: 0 4rpx;
}

.price-unit {
  font-size: 22rpx;
  color: #999;
}

.product-tags {
  display: flex;
  gap: 12rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #e8f5e8;
  color: #52c41a;
  font-size: 20rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 旧的网格样式已移除，使用瀑布流布局 */

.product-info {
  padding: 24rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.product-category {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4757;
  font-weight: 500;
}

.price-value {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

.product-status {
  display: flex;
  justify-content: flex-end;
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-tag.online {
  background: #e8f5e8;
  color: #52c41a;
}
</style>
