"""
公共接口控制器
不需要token验证的外部接口统一管理
"""
from fastapi import APIRouter, Depends, Request, Body, File, UploadFile, Query, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from datetime import datetime
from config.get_db import get_db
from config.env import AppConfig
from module_admin.entity.vo.public_vo import (
    OrderCreatedNotificationRequest,
    WechatMessageResponse,
    BatchNotificationRequest,
    BatchNotificationResponse,
    PublicApiResponse,
    HealthCheckResponse
)
from module_admin.service.wechat_official_service import WechatOfficialService
from module_admin.service.common_service import CommonService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import ValidationException, BusinessException

# 创建公共接口路由器，不包含认证依赖
public_controller = APIRouter(prefix='/api/v1/public', tags=['公共接口'])


@public_controller.get('/health', response_model=HealthCheckResponse, summary="健康检查接口")
async def health_check():
    """
    健康检查接口

    用于检查服务状态，不需要认证

    Returns:
        服务健康状态信息
    """
    try:
        return HealthCheckResponse(
            status="ok",
            version=AppConfig.app_version,
            timestamp=datetime.now().isoformat(),
            services={
                "database": "ok",
                "redis": "ok",
                "wechat": "ok"
            }
        )
    except Exception as e:
        logger.error(f"健康检查异常: {str(e)}")
        return HealthCheckResponse(
            status="error",
            version=AppConfig.app_version,
            timestamp=datetime.now().isoformat(),
            services={
                "database": "error",
                "redis": "error",
                "wechat": "error"
            }
        )


@public_controller.post('/wechat/order-created', response_model=WechatMessageResponse, summary="订单创建成功推送")
async def send_order_created_notification(
    request: Request,
    notification_data: OrderCreatedNotificationRequest = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    订单创建成功推送接口
    
    外部系统调用此接口推送订单创建成功消息到用户微信
    
    Args:
        request: HTTP请求对象
        notification_data: 推送数据
        db: 数据库会话
        
    Returns:
        推送结果
    """
    try:
        # 记录请求日志
        client_ip = request.client.host if request.client else "unknown"
        logger.info(f"收到订单创建推送请求 - IP: {client_ip}, 订单号: {notification_data.order_number}")
        
        # 验证必要参数
        if not notification_data.user_id and not notification_data.user_phone and not notification_data.store_uuid:
            raise ValidationException(message="用户ID、手机号或门店UUID至少提供一个")
        
        if not notification_data.order_number:
            raise ValidationException(message="订单编号不能为空")
            
        if not notification_data.service_name:
            raise ValidationException(message="服务名称不能为空")
            
        if not notification_data.service_time:
            raise ValidationException(message="服务时间不能为空")
        
        # 调用微信推送服务
        result = await WechatOfficialService.send_order_created_notification(
            db=db,
            user_id=notification_data.user_id,
            user_phone=notification_data.user_phone,
            store_uuid=notification_data.store_uuid,
            order_number=notification_data.order_number,
            service_name=notification_data.service_name,
            service_time=notification_data.service_time,
            order_status=notification_data.order_status or "已接单"
        )
        
        if result.get('success'):
            logger.info(f"订单创建推送成功 - 订单号: {notification_data.order_number}, 消息ID: {result.get('message_id')}")
            return WechatMessageResponse(
                success=True,
                message="推送成功",
                message_id=result.get('message_id'),
                send_time=result.get('send_time')
            )
        else:
            error_msg = result.get('message', '推送失败')
            logger.error(f"订单创建推送失败 - 订单号: {notification_data.order_number}, 错误: {error_msg}")
            return WechatMessageResponse(
                success=False,
                message=error_msg
            )
            
    except ValidationException as e:
        logger.error(f"订单创建推送参数验证失败: {str(e)}")
        return WechatMessageResponse(
            success=False,
            message=f"参数验证失败: {str(e)}"
        )
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"订单创建推送异常: {error_info['message']}")
        return WechatMessageResponse(
            success=False,
            message=f"推送异常: {error_info['message']}"
        )


@public_controller.post('/wechat/batch-order-created', response_model=BatchNotificationResponse, summary="批量订单创建推送")
async def send_batch_order_created_notifications(
    request: Request,
    batch_data: BatchNotificationRequest = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    批量订单创建成功推送接口

    外部系统调用此接口批量推送订单创建成功消息到用户微信

    Args:
        request: HTTP请求对象
        batch_data: 批量推送数据
        db: 数据库会话

    Returns:
        批量推送结果
    """
    try:
        # 记录请求日志
        client_ip = request.client.host if request.client else "unknown"
        logger.info(f"收到批量订单创建推送请求 - IP: {client_ip}, 数量: {len(batch_data.notifications)}")

        results = []
        success_count = 0
        failed_count = 0

        for notification in batch_data.notifications:
            try:
                # 验证必要参数
                if not notification.user_id and not notification.user_phone and not notification.store_uuid:
                    result = WechatMessageResponse(
                        success=False,
                        message="用户ID、手机号或门店UUID至少提供一个"
                    )
                    results.append(result)
                    failed_count += 1
                    continue

                # 调用微信推送服务
                push_result = await WechatOfficialService.send_order_created_notification(
                    db=db,
                    user_id=notification.user_id,
                    user_phone=notification.user_phone,
                    store_uuid=notification.store_uuid,
                    order_number=notification.order_number,
                    service_name=notification.service_name,
                    service_time=notification.service_time,
                    order_status=notification.order_status or "已接单"
                )

                if push_result.get('success'):
                    result = WechatMessageResponse(
                        success=True,
                        message="推送成功",
                        message_id=push_result.get('message_id'),
                        send_time=push_result.get('send_time')
                    )
                    success_count += 1
                else:
                    result = WechatMessageResponse(
                        success=False,
                        message=push_result.get('message', '推送失败')
                    )
                    failed_count += 1

                results.append(result)

            except Exception as e:
                error_info = ExceptionUtil.handle_exception(e)
                result = WechatMessageResponse(
                    success=False,
                    message=f"推送异常: {error_info['message']}"
                )
                results.append(result)
                failed_count += 1

        logger.info(f"批量订单创建推送完成 - 总数: {len(batch_data.notifications)}, 成功: {success_count}, 失败: {failed_count}")

        return BatchNotificationResponse(
            total=len(batch_data.notifications),
            success_count=success_count,
            failed_count=failed_count,
            results=results
        )

    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"批量订单创建推送异常: {error_info['message']}")
        return BatchNotificationResponse(
            total=0,
            success_count=0,
            failed_count=0,
            results=[]
        )


@public_controller.get('/aunt/resume/{aunt_uuid}', summary="获取阿姨简历详情（公开接口）")
async def get_aunt_resume_detail(
    aunt_uuid: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取阿姨简历详情（公开接口，无需token验证）

    Args:
        aunt_uuid: 阿姨UUID
        query_db: 数据库会话

    Returns:
        ResponseUtil: 包含阿姨简历详情的响应
    """
    try:
        logger.info(f"获取阿姨简历详情，UUID: {aunt_uuid}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.aunt_resume_service import AuntResumeService

        # 调用服务层获取简历详情
        resume_detail = await AuntResumeService.get_aunt_resume_detail(query_db, aunt_uuid)

        if not resume_detail:
            logger.warning(f"未找到阿姨简历信息，UUID: {aunt_uuid}")
            return ResponseUtil.failure(msg="未找到阿姨信息")

        logger.info(f"成功获取阿姨简历详情，UUID: {aunt_uuid}")
        return ResponseUtil.success(data=resume_detail)

    except Exception as e:
        logger.error(f"获取阿姨简历详情失败，UUID: {aunt_uuid}，错误: {str(e)}")
        return ResponseUtil.error(msg=f"获取阿姨简历详情失败: {str(e)}")


@public_controller.get('/aunt/resume/{aunt_uuid}/share', summary="获取阿姨简历分享信息")
async def get_aunt_resume_share_info(
    aunt_uuid: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取阿姨简历分享信息

    Args:
        aunt_uuid: 阿姨UUID
        query_db: 数据库会话

    Returns:
        ResponseUtil: 包含分享信息的响应
    """
    try:
        logger.info(f"获取阿姨简历分享信息，UUID: {aunt_uuid}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.aunt_resume_service import AuntResumeService

        # 调用服务层获取分享信息
        share_info = await AuntResumeService.get_aunt_resume_share_info(query_db, aunt_uuid)

        if not share_info:
            logger.warning(f"未找到阿姨分享信息，UUID: {aunt_uuid}")
            return ResponseUtil.failure(msg="未找到阿姨信息")

        logger.info(f"成功获取阿姨简历分享信息，UUID: {aunt_uuid}")
        return ResponseUtil.success(data=share_info)

    except Exception as e:
        logger.error(f"获取阿姨简历分享信息失败，UUID: {aunt_uuid}，错误: {str(e)}")
        return ResponseUtil.error(msg=f"获取阿姨简历分享信息失败: {str(e)}")


@public_controller.post('/upload', summary="公共文件上传接口（无需token验证）")
async def public_upload(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db)
):
    """公共文件上传接口（无需token验证）

    上传文件到华为云OBS并返回文件访问URL和相关信息
    不需要token验证，适用于外部系统调用

    - **file**: 要上传的文件
    """
    try:
        # 使用默认用户名
        user_name = 'public'

        upload_result = await CommonService.upload_service(file, None, user_name)
        logger.info(f'公共文件上传成功: {file.filename}')

        return ResponseUtil.success(data=upload_result)

    except Exception as e:
        logger.error(f"公共文件上传失败: {str(e)}")
        return ResponseUtil.error(msg=f"上传失败: {str(e)}")


@public_controller.get('/order/share-detail', summary="获取订单分享详情（无需token验证）")
async def get_order_share_detail(
    request: Request,
    order_number: str = Query(..., description="订单编号"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取订单分享详情接口（无需token验证）

    用于分享页面展示订单信息，任何人都可以访问

    :param request: Request对象
    :param order_number: 订单编号
    :param query_db: 数据库会话
    :return: 订单分享详情
    """
    try:
        logger.info(f"获取订单分享详情，订单号: {order_number}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.order_service import OrderService

        # 调用服务层获取订单分享详情
        result = await OrderService.get_order_share_detail_service(query_db, order_number)

        return ResponseUtil.success(
            msg="获取订单分享详情成功",
            data=result
        )
    except Exception as e:
        logger.error(f"获取订单分享详情失败，订单号: {order_number}，错误: {str(e)}")
        return ResponseUtil.error(msg=f"获取订单分享详情失败: {str(e)}")


@public_controller.post('/order/accept-by-share', summary="通过分享接单（无需token验证）")
async def accept_order_by_share(
    request: Request,
    order_number: str = Form(..., description="订单编号"),
    mobile: str = Form(..., description="员工手机号"),
    openid: str = Form(..., description="微信openid"),
    query_db: AsyncSession = Depends(get_db)
):
    """通过分享接单接口（无需token验证）

    用于分享页面的接单操作

    :param request: Request对象
    :param order_number: 订单编号
    :param mobile: 员工手机号
    :param openid: 微信openid
    :param query_db: 数据库会话
    :return: 接单结果
    """
    try:
        logger.info(f"通过分享接单，订单号: {order_number}, 手机号: {mobile}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.order_service import OrderService

        # 调用服务层处理分享接单
        result = await OrderService.accept_order_by_share_service(
            query_db, order_number, mobile, openid
        )

        return ResponseUtil.success(
            msg="接单成功",
            data=result
        )
    except Exception as e:
        logger.error(f"通过分享接单失败，订单号: {order_number}，错误: {str(e)}")
        return ResponseUtil.error(msg=f"接单失败: {str(e)}")


@public_controller.post('/order/check-staff-exists', summary="检查员工是否存在（无需token验证）")
async def check_staff_exists(
    request: Request,
    mobile: str = Form(..., description="手机号"),
    order_number: str = Form(None, description="订单号（可选，用于检查特定门店）"),
    query_db: AsyncSession = Depends(get_db)
):
    """检查员工是否存在接口（无需token验证）

    用于分享页面验证用户身份，支持检查员工在特定门店的存在状态

    :param request: Request对象
    :param mobile: 手机号
    :param order_number: 订单号（可选，用于获取门店信息）
    :param query_db: 数据库会话
    :return: 员工存在状态
    """
    try:
        logger.info(f"[DEBUG] 检查员工是否存在开始，手机号: {mobile}, 订单号: {order_number}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.order_service import OrderService

        # 调用服务层检查员工是否存在
        result = await OrderService.check_staff_exists_service(query_db, mobile, order_number)

        logger.info(f"[DEBUG] 服务层调用成功，返回结果: {result}")
        return ResponseUtil.success(
            msg="检查员工状态成功",
            data=result
        )
    except Exception as e:
        logger.error(f"检查员工是否存在失败，手机号: {mobile}，错误: {str(e)}")
        return ResponseUtil.error(msg=f"检查员工状态失败: {str(e)}")


@public_controller.post('/order/copy-staff-to-store', summary="复制员工到指定门店（无需token验证）")
async def copy_staff_to_store(
    request: Request,
    mobile: str = Form(..., description="手机号"),
    order_number: str = Form(..., description="订单号（用于获取目标门店信息）"),
    openid: str = Form(..., description="微信openid"),
    query_db: AsyncSession = Depends(get_db)
):
    """复制员工到指定门店接口（无需token验证）

    将员工信息从其他门店复制到订单对应的门店

    :param request: Request对象
    :param mobile: 手机号
    :param order_number: 订单号（用于获取目标门店信息）
    :param openid: 微信openid
    :param query_db: 数据库会话
    :return: 复制结果
    """
    try:
        logger.info(f"复制员工到门店，手机号: {mobile}, 订单号: {order_number}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.order_service import OrderService

        # 调用服务层复制员工到门店
        result = await OrderService.copy_staff_to_store_service(query_db, mobile, order_number, openid)

        return ResponseUtil.success(
            msg="员工信息同步成功",
            data=result
        )
    except Exception as e:
        logger.error(f"复制员工到门店失败，手机号: {mobile}，错误: {str(e)}")
        return ResponseUtil.error(msg=f"员工信息同步失败: {str(e)}")


@public_controller.get('/product/products', summary="获取产品列表（无需token验证）")
async def get_products_public(
    request: Request,
    invitation_code: str = Query(..., description="邀请码，必须提供以获取对应公司的产品"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取产品列表接口（无需token验证）

    用于员工入驻页面获取产品列表，无需认证
    必须提供有效的邀请码，只返回邀请人所属公司的产品

    :param request: Request对象
    :param invitation_code: 邀请码（必需），用于获取对应公司的产品
    :param query_db: 数据库会话
    :return: 产品列表
    """
    try:
        logger.info(f"获取产品列表（公开接口），邀请码: {invitation_code}")

        # 导入服务层（延迟导入避免循环依赖）
        from module_admin.service.product_service import ProductService
        from sqlalchemy import text

        # 验证邀请码并获取对应的公司UUID
        inviter_query = text("""
            SELECT company_id, name as inviter_name, store_name
            FROM internal_user
            WHERE id = :invitation_code AND status = '1'
            LIMIT 1
        """)
        inviter_result = await query_db.execute(inviter_query, {"invitation_code": invitation_code})
        inviter_row = inviter_result.fetchone()

        if not inviter_row:
            logger.warning(f"邀请码 {invitation_code} 无效或邀请人不存在")
            return ResponseUtil.error(msg="邀请码无效或邀请人不存在，无法获取产品列表")

        company_uuid = inviter_row.company_id
        logger.info(f"根据邀请码 {invitation_code} 获取到公司UUID: {company_uuid}，邀请人: {inviter_row.inviter_name}")

        # 调用服务层获取产品列表（只返回该公司的产品）
        result = await ProductService.get_products_service(query_db, company_uuid)

        return ResponseUtil.success(
            msg=f"获取{inviter_row.store_name}产品列表成功",
            data=result
        )
    except Exception as e:
        logger.error(f"获取产品列表失败，错误: {str(e)}")
        return ResponseUtil.error(msg=f"获取产品列表失败: {str(e)}")


@public_controller.get('/product/invitation-info/{invitation_code}', summary="根据邀请码获取门店信息（无需token验证）")
async def get_invitation_info_public(
    request: Request,
    invitation_code: str,
    query_db: AsyncSession = Depends(get_db)
):
    """根据邀请码获取门店信息接口（无需token验证）

    用于员工入驻页面验证邀请码并获取门店信息，无需认证

    :param request: Request对象
    :param invitation_code: 邀请码（internal_user表的ID）
    :param query_db: 数据库会话
    :return: 门店信息和邀请人信息
    """
    try:
        logger.info(f"获取邀请码信息（公开接口），邀请码: {invitation_code}")

        # 导入依赖（延迟导入避免循环依赖）
        from sqlalchemy import text

        # 根据邀请码查询邀请人和门店信息
        query = text("""
            SELECT
                u.id as inviter_id,
                u.name as inviter_name,
                u.role_name as inviter_role,
                u.store_id,
                u.store_name,
                u.store_uuid,
                u.company_id,
                s.address as store_address,
                s.phone as store_phone,
                s.manager as store_manager
            FROM internal_user u
            LEFT JOIN store s ON u.store_id = s.id
            WHERE u.id = :inviter_id AND u.status = '1' AND s.status = 1 AND s.is_delete = 0
            LIMIT 1
        """)

        result = await query_db.execute(query, {"inviter_id": invitation_code})
        row = result.fetchone()

        if not row:
            return ResponseUtil.error(msg="邀请码无效或邀请人不存在")

        invitation_info = {
            "inviter_id": row.inviter_id,
            "inviter_name": row.inviter_name,
            "inviter_role": row.inviter_role,
            "store_id": row.store_id,
            "store_name": row.store_name,
            "store_uuid": row.store_uuid,
            "store_address": row.store_address,
            "store_phone": row.store_phone,
            "store_manager": row.store_manager,
            "company_id": row.company_id
        }

        logger.info(f"邀请码验证成功，邀请人: {invitation_info['inviter_name']}, 门店: {invitation_info['store_name']}")

        return ResponseUtil.success(
            data=invitation_info,
            msg="获取邀请信息成功"
        )

    except Exception as e:
        logger.error(f"获取邀请码信息失败，错误: {str(e)}")
        return ResponseUtil.error(msg=f"获取门店信息失败: {str(e)}")


@public_controller.post('/product/staff/register-by-invitation', summary="通过邀请码注册员工（无需token验证）")
async def register_staff_by_invitation_public(
    request: Request,
    staff_data: dict,
    invitation_code: str = Query(..., description="邀请码"),
    query_db: AsyncSession = Depends(get_db)
):
    """通过邀请码注册员工接口（无需token验证）

    用于分享页面的员工快速入驻，无需认证

    :param request: Request对象
    :param staff_data: 员工注册数据
    :param invitation_code: 邀请码
    :param query_db: 数据库会话
    :return: 注册结果
    """
    try:
        logger.info(f"通过邀请码注册员工（公开接口），邀请码: {invitation_code}")

        # 导入服务层和模型（延迟导入避免循环依赖）
        from module_admin.service.product_service import ProductService
        from module_admin.entity.vo.staff_registration_vo import StaffRegistrationRequestModel
        from sqlalchemy import text

        # 将字典转换为模型
        staff_model = StaffRegistrationRequestModel(**staff_data)

        # 1. 验证邀请码，获取邀请人和门店信息
        inviter_query = text("""
            SELECT
                u.id as inviter_id,
                u.uuid as inviter_uuid,
                u.name as inviter_name,
                u.store_id,
                u.store_name,
                u.store_uuid,
                u.company_id
            FROM internal_user u
            WHERE u.id = :inviter_id AND u.status = '1'
            LIMIT 1
        """)

        inviter_result = await query_db.execute(inviter_query, {"inviter_id": invitation_code})
        inviter_row = inviter_result.fetchone()

        if not inviter_row:
            return ResponseUtil.error(msg="邀请码无效或邀请人不存在")

        store_id = str(inviter_row.store_id)
        company_id = str(inviter_row.company_id)
        inviter_name = inviter_row.inviter_name

        # 2. 调用服务层进行员工入驻
        result = await ProductService.register_staff(
            db=query_db,
            staff_data=staff_model,
            current_user_id=inviter_row.inviter_uuid,  # 使用邀请人UUID作为创建者
            company_id=company_id,
            store_id=store_id,
            invitation_code=invitation_code
        )

        logger.info(f"通过邀请码注册员工成功，邀请人: {inviter_name}, 员工ID: {result.staff_id}")

        return ResponseUtil.success(
            data={
                **result.model_dump(),
                "inviter_name": inviter_name,
                "store_name": inviter_row.store_name
            },
            msg=f"员工入驻成功，欢迎加入{inviter_row.store_name}！"
        )

    except Exception as e:
        logger.error(f"通过邀请码注册员工失败，错误: {str(e)}")
        return ResponseUtil.error(msg=f"员工入驻失败: {str(e)}")


@public_controller.get('/file/download/{file_id}', summary="公开文件下载接口（无需token验证）")
async def public_file_download(
    file_id: str,
    query_db: AsyncSession = Depends(get_db)
):
    """公开文件下载接口（无需token验证）

    根据文件ID获取文件URL，用于图片等资源的访问

    Args:
        file_id: 文件ID
        query_db: 数据库会话

    Returns:
        重定向到文件URL或返回文件内容
    """
    try:
        from module_admin.service.file_service import FileService
        from fastapi.responses import RedirectResponse
        from sqlalchemy import text

        logger.info(f"公开文件下载请求，文件ID: {file_id}")

        # 查询文件信息
        file_query = text("""
            SELECT fi.file_url, fi.file_name, fi.file_type
            FROM file_item fi
            INNER JOIN file_main fm ON fi.main_id = fm.id
            WHERE fm.id = :file_id
            ORDER BY fi.created_at ASC
            LIMIT 1
        """)

        file_result = await query_db.execute(file_query, {"file_id": file_id})
        file_row = file_result.fetchone()

        if not file_row:
            logger.warning(f"文件不存在，文件ID: {file_id}")
            return ResponseUtil.error(msg="文件不存在", code=404)

        file_url = file_row.file_url

        if not file_url:
            logger.warning(f"文件URL为空，文件ID: {file_id}")
            return ResponseUtil.error(msg="文件URL无效", code=404)

        logger.info(f"文件下载成功，重定向到: {file_url}")

        # 重定向到实际的文件URL
        return RedirectResponse(url=file_url, status_code=302)

    except Exception as e:
        logger.error(f"公开文件下载失败: {str(e)}")
        return ResponseUtil.error(msg=f"文件下载失败: {str(e)}")
